"""
LangGraph Agent Core for AP3X Crypto Agent Backend
Implements the core reasoning workflow with proper MCP integration
"""

import asyncio
import re
import time
from datetime import datetime
from typing import Dict, Any, List, Optional

import structlog
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import StateGraph, Graph, END
from langgraph.checkpoint.memory import MemorySaver

from .state import AgentState
from .tools import (
    get_crypto_price,
    get_wallet_balance,
    get_wallet_nfts,
    search_web,
    get_defi_info,
    get_trending_tokens,
    analyze_transaction,
    sequential_thinking
)
from services.mcp_manager import MCPClientManager, get_moralis_wallet_info, search_web_content, fetch_url_content
from core.config import get_llm_config

logger = structlog.get_logger()


class LangGraphAgent:
    """LangGraph agent with proper MCP integration"""
    
    def __init__(self, use_checkpointer: bool = True, settings=None):
        # Get LLM configuration
        llm_config = get_llm_config()
        
        # Initialize OpenRouter LLM
        self.llm = ChatOpenAI(
            model=llm_config["model"],
            openai_api_key=llm_config["api_key"],
            openai_api_base=llm_config["base_url"],
            timeout=llm_config["timeout"],
            default_headers={
                "HTTP-Referer": "https://localhost:3000",
                "X-Title": "AP3X Crypto Agent"
            }
        )

        # Initialize MCP client manager
        self.mcp_manager: Optional[MCPClientManager] = None
        if settings:
            self.mcp_manager = MCPClientManager(settings)

        # Setup checkpointer if requested
        self.checkpointer = None
        if use_checkpointer:
            self.checkpointer = MemorySaver()

        # Build the graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> Graph:
        """Build the LangGraph workflow with improved patterns"""
        from langgraph.graph import START

        workflow = StateGraph(AgentState)

        # Add nodes with descriptive names
        workflow.add_node("analyze_problem", self._analyze_problem)
        workflow.add_node("fetch_blockchain_data", self._fetch_blockchain_data)
        workflow.add_node("conduct_web_research", self._conduct_web_research)
        workflow.add_node("sequential_thinking", self._sequential_thinking)
        workflow.add_node("validate_thinking", self._validate_thinking)
        workflow.add_node("synthesize_answer", self._synthesize_answer)

        # Add edges using START constant
        workflow.add_edge(START, "analyze_problem")
        workflow.add_conditional_edges(
            "analyze_problem",
            self._route_data_collection,
            {
                "blockchain": "fetch_blockchain_data",
                "web_research": "conduct_web_research",
                "both": "fetch_blockchain_data",
                "continue": "sequential_thinking"
            }
        )
        workflow.add_edge("fetch_blockchain_data", "conduct_web_research")
        workflow.add_edge("conduct_web_research", "sequential_thinking")
        workflow.add_conditional_edges(
            "sequential_thinking",
            self._should_continue_thinking,
            {
                "continue": "sequential_thinking",
                "validate": "validate_thinking"
            }
        )
        workflow.add_edge("validate_thinking", "synthesize_answer")
        workflow.add_edge("synthesize_answer", END)

        # Compile with checkpointer if available
        compile_kwargs = {}
        if self.checkpointer:
            compile_kwargs["checkpointer"] = self.checkpointer

        return workflow.compile(**compile_kwargs)
    
    async def _analyze_problem(self, state: AgentState) -> AgentState:
        """Analyze the problem and break it down"""
        last_message = state.messages[-1]["content"] if state.messages else ""
        
        analysis_prompt = f"""
        Analyze this crypto/blockchain problem and break it down into logical thinking steps:
        
        Problem: {last_message}
        
        Determine what data sources are needed:
        - Blockchain data (wallet balances, NFTs, token prices, transactions)
        - Web research (latest news, market trends, technical analysis)
        - Social sentiment (Twitter discussions, community sentiment)
        
        Provide a structured analysis of what needs to be considered.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=analysis_prompt)])
        
        # Initialize MCP session if available
        session_id = f"session_{int(time.time())}"
        state.session_id = session_id
        state.problem_analysis = response.content

        # Check if the problem requires blockchain data
        blockchain_keywords = ["wallet", "nft", "token", "ethereum", "blockchain", "crypto", "defi", "0x", "balance", "price"]
        if any(keyword in last_message.lower() for keyword in blockchain_keywords):
            state.requires_blockchain_data = True
            # Extract wallet addresses if present
            import re
            wallet_pattern = r'0x[a-fA-F0-9]{40}'
            addresses = re.findall(wallet_pattern, last_message)
            state.wallet_addresses = addresses

        # Check if the problem requires web research
        research_keywords = ["latest", "current", "recent", "news", "trends", "what is", "how to", "compare", "analysis", "market"]
        if any(keyword in last_message.lower() for keyword in research_keywords):
            state.requires_web_research = True
            # Generate search queries based on the problem
            state.search_queries = [last_message]

        state.messages.append({
            "role": "assistant",
            "content": f"Analysis: {response.content}",
            "timestamp": datetime.now().isoformat()
        })

        logger.info("Problem analyzed", 
                   requires_blockchain=state.requires_blockchain_data,
                   requires_web_research=state.requires_web_research,
                   wallet_addresses=len(state.wallet_addresses))

        return state
    
    def _route_data_collection(self, state: AgentState) -> str:
        """Route to appropriate data collection based on requirements"""
        needs_blockchain = state.requires_blockchain_data
        needs_web_research = state.requires_web_research

        if needs_blockchain and needs_web_research:
            return "both"
        elif needs_blockchain:
            return "blockchain"
        elif needs_web_research:
            return "web_research"
        else:
            return "continue"
    
    async def _fetch_blockchain_data(self, state: AgentState) -> AgentState:
        """Fetch blockchain and crypto data using real tools"""
        logger.info("Fetching blockchain data", query=state.query)

        # Analyze query to determine what blockchain data to fetch
        query_lower = state.query.lower()

        try:
            blockchain_data = {}

            # Check for wallet addresses in the query
            wallet_pattern = r'0x[a-fA-F0-9]{40}'
            wallet_addresses = re.findall(wallet_pattern, state.query)

            if wallet_addresses:
                for address in wallet_addresses:
                    try:
                        # Get wallet token balance
                        wallet_balance = await get_wallet_balance.ainvoke({"address": address})
                        blockchain_data[f"wallet_balance_{address}"] = wallet_balance

                        # Get wallet NFTs
                        wallet_nfts = await get_wallet_nfts.ainvoke({"address": address})
                        blockchain_data[f"wallet_nfts_{address}"] = wallet_nfts

                        state.wallet_addresses.append(address)
                    except Exception as e:
                        logger.warning("Failed to fetch wallet data", address=address, error=str(e))
                        blockchain_data[f"wallet_{address}"] = f"Error fetching data: {str(e)}"

            # Check for price queries
            if any(term in query_lower for term in ['price', 'cost', 'value', 'worth']):
                # Extract potential token names
                tokens = ['bitcoin', 'btc', 'ethereum', 'eth', 'usdc', 'usdt', 'link', 'uni']
                for token in tokens:
                    if token in query_lower:
                        try:
                            price_data = await get_crypto_price.ainvoke({"token": token})
                            blockchain_data[f"price_{token}"] = price_data
                        except Exception as e:
                            logger.warning("Failed to fetch price data", token=token, error=str(e))

            # Check for DeFi queries
            if any(term in query_lower for term in ['defi', 'uniswap', 'aave', 'compound', 'yield', 'liquidity']):
                try:
                    # Extract protocol name if mentioned
                    protocol = "overview"
                    if "uniswap" in query_lower:
                        protocol = "uniswap"
                    elif "aave" in query_lower:
                        protocol = "aave"
                    elif "compound" in query_lower:
                        protocol = "compound"

                    defi_data = await get_defi_info.ainvoke({"protocol": protocol})
                    blockchain_data["defi_info"] = defi_data
                except Exception as e:
                    logger.warning("Failed to fetch DeFi data", error=str(e))

            # Check for trending/market queries
            if any(term in query_lower for term in ['trending', 'top', 'gainers', 'losers', 'market']):
                try:
                    trending_data = await get_trending_tokens.ainvoke({})
                    blockchain_data["trending_tokens"] = trending_data
                except Exception as e:
                    logger.warning("Failed to fetch trending data", error=str(e))

            # Check for transaction hash
            tx_pattern = r'0x[a-fA-F0-9]{64}'
            tx_hashes = re.findall(tx_pattern, state.query)
            if tx_hashes:
                for tx_hash in tx_hashes:
                    try:
                        tx_analysis = await analyze_transaction.ainvoke({"tx_hash": tx_hash})
                        blockchain_data[f"transaction_{tx_hash}"] = tx_analysis
                    except Exception as e:
                        logger.warning("Failed to analyze transaction", tx_hash=tx_hash, error=str(e))

            state.blockchain_data = blockchain_data
            state.mcp_results["blockchain"] = blockchain_data

            logger.info("Blockchain data fetched successfully")

        except Exception as e:
            logger.error("Error fetching blockchain data", error=str(e))
            state.blockchain_data = {"error": f"Failed to fetch blockchain data: {str(e)}"}

        return state
    
    async def _conduct_web_research(self, state: AgentState) -> AgentState:
        """Conduct web research using MCP clients"""
        web_research_data = {}

        try:
            if self.mcp_manager:
                if not state.search_queries:
                    # Generate search queries if none exist
                    problem = state.messages[0]["content"] if state.messages else ""
                    state.search_queries = [problem]

                logger.info("Conducting web research", queries=len(state.search_queries))

                for query in state.search_queries:
                    try:
                        # Use real web search tool
                        search_result = await search_web.ainvoke({"query": query, "max_results": 5})
                        web_research_data[query] = search_result
                        state.mcp_tools_used.append("web_search")
                    except Exception as e:
                        logger.error("Failed to search web content", query=query, error=str(e))
                        web_research_data[query] = {"error": str(e)}

                state.web_research_data = web_research_data
                state.mcp_results["web_research"] = web_research_data
                
                logger.info("Web research completed", queries=len(state.search_queries))
            
            else:
                logger.warning("MCP manager not available, skipping web research")
                state.web_research_data = {"error": "MCP manager not initialized"}

        except Exception as e:
            logger.error("Error conducting web research", error=str(e))
            state.web_research_data = {"error": str(e)}

        return state
    
    async def _sequential_thinking(self, state: AgentState) -> AgentState:
        """Perform sequential thinking with available data"""
        problem = state.messages[0]["content"] if state.messages else ""
        
        # Build context from collected data
        context_parts = [f"Problem: {problem}"]
        
        if state.blockchain_data:
            context_parts.append(f"Blockchain Data Available: {len(state.blockchain_data)} sources")
        
        if state.web_research_data:
            context_parts.append(f"Web Research Data Available: {len(state.web_research_data)} queries")
        
        thinking_prompt = f"""
        Think through this crypto/blockchain problem step by step using the available data:
        
        {chr(10).join(context_parts)}
        
        Current thinking steps so far: {len(state.thinking_steps)}
        
        Provide your next reasoning step. Build on previous analysis and incorporate any available data.
        Be specific and actionable in your reasoning.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=thinking_prompt)])
        
        # Add the new thinking step
        new_step = response.content.strip()
        if new_step:
            state.thinking_steps.append(new_step)
            state.mcp_tools_used.append("sequential_thinking")

            # Optional: Use MCP sequential thinking if available and properly configured
            if self.mcp_manager and self.mcp_manager._initialized:
                try:
                    # Only try MCP if we have a valid session and the tool exists
                    if state.session_id and hasattr(self.mcp_manager, 'multi_client'):
                        await self.mcp_manager.call_tool("sequential_thinking", "add_step", {
                            "session_id": state.session_id,
                            "step": new_step
                        })
                        logger.debug("Used MCP sequential thinking successfully")
                except Exception as e:
                    # Don't log as warning since this is optional functionality
                    logger.debug("MCP sequential thinking not available", error=str(e))

        state.current_step = len(state.thinking_steps)
        
        logger.info("Sequential thinking step added", 
                   step_number=state.current_step,
                   total_steps=len(state.thinking_steps))

        return state
    
    def _should_continue_thinking(self, state: AgentState) -> str:
        """Determine if we should continue thinking or move to validation"""
        # Simple heuristic: if we have fewer than 3 steps, continue thinking
        if len(state.thinking_steps) < 3:
            return "continue"
        return "validate"
    
    async def _validate_thinking(self, state: AgentState) -> AgentState:
        """Validate the thinking process and assign confidence score"""
        validation_prompt = f"""
        Review the following thinking steps and assess their quality:

        Steps:
        {chr(10).join(state.thinking_steps)}

        Rate the logical consistency and completeness on a scale of 0.0 to 1.0.
        Provide a brief assessment.
        """

        response = await self.llm.ainvoke([HumanMessage(content=validation_prompt)])

        # Extract confidence score (simplified - in practice, use structured output)
        content = response.content.lower()
        if "0.9" in content or "0.8" in content:
            state.confidence_score = 0.9
        elif "0.7" in content or "0.6" in content:
            state.confidence_score = 0.7
        else:
            state.confidence_score = 0.5

        return state
    
    async def _synthesize_answer(self, state: AgentState) -> AgentState:
        """Synthesize final answer using all collected data"""
        
        # Build comprehensive context
        context_parts = []
        
        if state.problem_analysis:
            context_parts.append(f"Problem Analysis: {state.problem_analysis}")
        
        if state.thinking_steps:
            context_parts.append(f"Thinking Steps:\n{chr(10).join(f'{i+1}. {step}' for i, step in enumerate(state.thinking_steps))}")
        
        if state.blockchain_data and not state.blockchain_data.get("error"):
            context_parts.append(f"Blockchain Data: Available for analysis")
        
        if state.web_research_data and not state.web_research_data.get("error"):
            context_parts.append(f"Web Research: Available for analysis")
        
        if state.mcp_tools_used:
            context_parts.append(f"MCP Tools Used: {', '.join(state.mcp_tools_used)}")

        synthesis_prompt = f"""
        Based on the comprehensive analysis below, provide a clear, final answer to the user's question:

        {chr(10).join(context_parts)}

        Confidence Score: {state.confidence_score}

        Instructions:
        1. Provide a concise, well-reasoned final answer
        2. Reference specific data points when available (wallet balances, token prices, research findings)
        3. Acknowledge any limitations or missing data
        4. Structure the response clearly with key insights highlighted
        5. If this is a crypto/blockchain query, provide actionable insights where appropriate
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=synthesis_prompt)])
        
        state.final_answer = response.content
        state.messages.append({
            "role": "assistant",
            "content": response.content,
            "timestamp": datetime.now().isoformat(),
            "confidence_score": state.confidence_score,
            "tools_used": state.mcp_tools_used
        })
        
        logger.info("Final answer synthesized", 
                   confidence=state.confidence_score,
                   tools_used=len(state.mcp_tools_used),
                   answer_length=len(response.content))
        
        return state
    
    async def initialize_mcp(self):
        """Initialize MCP client manager"""
        if self.mcp_manager:
            await self.mcp_manager.initialize()
            logger.info("MCP client manager initialized")
    
    async def cleanup_mcp(self):
        """Cleanup MCP client manager"""
        if self.mcp_manager:
            await self.mcp_manager.cleanup()
            logger.info("MCP client manager cleaned up")

    async def run(self, user_input: str, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Run the agent with user input and optional configuration"""
        start_time = time.time()
        
        try:
            # Initialize MCP if not already done
            if self.mcp_manager and not self.mcp_manager._initialized:
                await self.initialize_mcp()
            
            initial_state = AgentState(
                messages=[{"role": "user", "content": user_input}],
                query=user_input
            )

            # Use config for thread management if provided
            final_state = await self.graph.ainvoke(initial_state, config=config)

            processing_time = time.time() - start_time

            # Handle different return types from LangGraph
            if hasattr(final_state, 'final_answer'):
                # If it's an AgentState object
                return {
                    "final_answer": final_state.final_answer,
                    "thinking_steps": final_state.thinking_steps,
                    "confidence_score": final_state.confidence_score,
                    "problem_analysis": final_state.problem_analysis,
                    "session_id": final_state.session_id,
                    "blockchain_data": final_state.blockchain_data,
                    "wallet_addresses": final_state.wallet_addresses,
                    "web_research_data": final_state.web_research_data,
                    "search_queries": final_state.search_queries,
                    "mcp_tools_used": final_state.mcp_tools_used,
                    "mcp_results": final_state.mcp_results,
                    "messages": final_state.messages,
                    "processing_time": processing_time,
                    "status": "success"
                }
            elif isinstance(final_state, dict):
                # If it's a dictionary (AddableValuesDict)
                return {
                    "final_answer": final_state.get("final_answer", "Response completed"),
                    "thinking_steps": final_state.get("thinking_steps", []),
                    "confidence_score": final_state.get("confidence_score", 0.5),
                    "problem_analysis": final_state.get("problem_analysis", ""),
                    "session_id": final_state.get("session_id", ""),
                    "blockchain_data": final_state.get("blockchain_data", {}),
                    "wallet_addresses": final_state.get("wallet_addresses", []),
                    "web_research_data": final_state.get("web_research_data", {}),
                    "search_queries": final_state.get("search_queries", []),
                    "mcp_tools_used": final_state.get("mcp_tools_used", []),
                    "mcp_results": final_state.get("mcp_results", {}),
                    "messages": final_state.get("messages", []),
                    "processing_time": processing_time,
                    "status": "success"
                }
            else:
                # Fallback for unknown types
                logger.warning("Unknown final_state type", type=type(final_state))
                return {
                    "final_answer": "Response completed successfully",
                    "thinking_steps": [],
                    "confidence_score": 0.5,
                    "processing_time": processing_time,
                    "status": "success"
                }
        except Exception as e:
            logger.error("Agent execution failed", error=str(e))
            return {
                "final_answer": None,
                "thinking_steps": [],
                "confidence_score": 0.0,
                "processing_time": time.time() - start_time,
                "error": str(e),
                "status": "error"
            }

    async def stream_run(self, user_input: str, config: Optional[Dict[str, Any]] = None):
        """Stream the agent execution for real-time updates"""
        initial_state = AgentState(
            messages=[{"role": "user", "content": user_input}],
            query=user_input
        )

        async for event in self.graph.astream(initial_state, config=config):
            yield event
