"""
Agent State Definitions for AP3X Crypto Agent
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field


class AgentState(BaseModel):
    """State for the LangGraph agent with MCP integration"""
    messages: List[Dict[str, Any]] = Field(default_factory=list)
    query: Optional[str] = None  # Current user query
    thinking_steps: List[str] = Field(default_factory=list)
    current_step: int = 0
    final_answer: Optional[str] = None
    session_id: Optional[str] = None
    problem_analysis: Optional[str] = None
    confidence_score: float = 0.0
    
    # Blockchain-related fields
    blockchain_data: Dict[str, Any] = Field(default_factory=dict)
    wallet_addresses: List[str] = Field(default_factory=list)
    requires_blockchain_data: bool = False
    
    # Web research fields
    web_research_data: Dict[str, Any] = Field(default_factory=dict)
    search_queries: List[str] = Field(default_factory=list)
    requires_web_research: bool = False
    
    # MCP-specific fields
    mcp_tools_used: List[str] = Field(default_factory=list)
    mcp_results: Dict[str, Any] = Field(default_factory=dict)
