"""
Real Crypto Tools for LangGraph Agent using Moralis API
"""

import asyncio
import aiohttp
import json
import re
from typing import List, Dict, Any, Optional
from langchain_core.tools import tool
import structlog

logger = structlog.get_logger()

# Common crypto token addresses for price lookups
COMMON_TOKENS = {
    "bitcoin": "******************************************",  # WBTC on Ethereum
    "btc": "******************************************",
    "ethereum": "******************************************",  # WETH
    "eth": "******************************************",
    "usdc": "******************************************",
    "usdt": "******************************************",
    "link": "******************************************",
    "uni": "******************************************",
}

async def make_moralis_request(endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """Make a request to Moralis API with proper error handling"""
    api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImI1YmVhMTc1LWU5MmEtNGU0ZS1hOTgxLWU3ZjJlNTk5NjYyMCIsIm9yZ0lkIjoiNDA1NzgzIiwidXNlcklkIjoiNDE2OTY2IiwidHlwZUlkIjoiZTYyZmNjOGMtMTZlMi00ZmY1LWJjMjItNzVlYTBhZTRjNDk2IiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3MjQ1MTYzOTcsImV4cCI6NDg4MDI3NjM5N30.73TV_J52D11itXTgHvc1wr_kavMbyHAcxCya1TNEoeI"

    base_url = "https://deep-index.moralis.io/api/v2.2"
    url = f"{base_url}/{endpoint}"

    headers = {
        "X-API-Key": api_key,
        "Content-Type": "application/json"
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    logger.error("Moralis API error", status=response.status, error=error_text)
                    return {"error": f"API error {response.status}: {error_text}"}

    except Exception as e:
        logger.error("Moralis API request failed", error=str(e))
        # Fallback to mock data if API fails
        if "token/price" in endpoint:
            return await get_mock_token_price(params)
        elif "wallets" in endpoint and "tokens" in endpoint:
            return await get_mock_wallet_tokens(params)
        elif "wallets" in endpoint and "nfts" in endpoint:
            return await get_mock_wallet_nfts(params)
        else:
            return {"error": f"API request failed: {str(e)}"}

async def get_mock_token_price(params: Dict[str, Any]) -> Dict[str, Any]:
    """Return mock token price data"""
    token_address = params.get("address", "").lower()

    # Mock prices for common tokens
    mock_prices = {
        "******************************************": {"usdPrice": 97500.50, "symbol": "WBTC", "name": "Wrapped Bitcoin"},
        "******************************************": {"usdPrice": 3420.75, "symbol": "WETH", "name": "Wrapped Ethereum"},
        "******************************************": {"usdPrice": 1.00, "symbol": "USDC", "name": "USD Coin"},
        "******************************************": {"usdPrice": 0.999, "symbol": "USDT", "name": "Tether USD"},
    }

    return mock_prices.get(token_address, {"usdPrice": 0, "symbol": "UNKNOWN", "name": "Unknown Token"})

async def get_mock_wallet_tokens(params: Dict[str, Any]) -> Dict[str, Any]:
    """Return mock wallet token data"""
    return {
        "result": [
            {
                "token_address": "******************************************",
                "symbol": "WETH",
                "name": "Wrapped Ethereum",
                "balance": "1500000000000000000",  # 1.5 ETH
                "decimals": 18,
                "usd_value": 5131.125
            },
            {
                "token_address": "******************************************",
                "symbol": "USDC",
                "name": "USD Coin",
                "balance": "10000000000",  # 10,000 USDC
                "decimals": 6,
                "usd_value": 10000.00
            }
        ]
    }

async def get_mock_wallet_nfts(params: Dict[str, Any]) -> Dict[str, Any]:
    """Return mock wallet NFT data"""
    return {
        "result": [
            {
                "token_address": "******************************************",
                "token_id": "1234",
                "name": "Bored Ape Yacht Club",
                "symbol": "BAYC",
                "metadata": {
                    "name": "Bored Ape #1234",
                    "description": "A unique Bored Ape NFT",
                    "image": "https://example.com/bayc1234.png"
                }
            }
        ]
    }


@tool
async def get_crypto_price(token: str, chain: str = "eth") -> str:
    """Get the current price of a cryptocurrency token"""
    try:
        # Normalize token name
        token_lower = token.lower()

        # Check if it's a common token
        if token_lower in COMMON_TOKENS:
            token_address = COMMON_TOKENS[token_lower]
        else:
            # Assume it's already an address if it starts with 0x
            if token.startswith("0x") and len(token) == 42:
                token_address = token
            else:
                return f"❌ Could not find token address for '{token}'. Please provide a valid token symbol (BTC, ETH, USDC) or contract address."

        # Get price data using real Moralis API
        price_data = await make_moralis_request(f"evm/{token_address}/price", {"chain": chain})

        if "error" in price_data:
            return f"❌ Error fetching price: {price_data['error']}"

        # Extract price information
        price = price_data.get("usdPrice")
        if price is None:
            price = price_data.get("price", 0)

        symbol = price_data.get("symbol", token.upper())
        name = price_data.get("name", "Unknown Token")

        # Format price
        if isinstance(price, str):
            try:
                price = float(price)
            except:
                price = 0

        return f"💰 **{name} ({symbol}) Price**\n" \
               f"💵 **${price:,.2f} USD**\n" \
               f"🔗 Chain: {chain.upper()}\n" \
               f"📊 Data from Moralis API"

    except Exception as e:
        logger.error("Error getting crypto price", token=token, error=str(e))
        return f"❌ Error getting price for {token}: {str(e)}"


@tool
async def get_wallet_balance(address: str, chain: str = "eth") -> str:
    """Get wallet balance and token holdings using Moralis API"""
    try:
        # Validate wallet address
        if not address.startswith("0x") or len(address) != 42:
            return f"❌ Invalid wallet address format. Please provide a valid Ethereum address starting with 0x"

        # Get wallet token balances using real Moralis API
        balance_data = await make_moralis_request(f"wallets/{address}/tokens", {"chain": chain})

        if "error" in balance_data:
            return f"❌ Error fetching wallet data: {balance_data['error']}"

        tokens = balance_data.get("result", [])

        if not tokens:
            return f"🔍 **Wallet Analysis: {address[:10]}...{address[-8:]}**\n" \
                   f"💰 No tokens found or wallet is empty\n" \
                   f"🔗 Chain: {chain.upper()}"

        # Format response
        response = f"🔍 **Wallet Analysis: {address[:10]}...{address[-8:]}**\n"
        response += f"🔗 Chain: {chain.upper()}\n\n"
        response += "💰 **Token Holdings:**\n"

        total_value = 0
        for token in tokens[:10]:  # Limit to top 10 tokens
            symbol = token.get("symbol", "UNKNOWN")
            name = token.get("name", "Unknown Token")
            balance = token.get("balance", "0")
            decimals = int(token.get("decimals", 18))

            # Convert balance from wei to human readable
            try:
                balance_wei = int(balance)
                readable_balance = balance_wei / (10 ** decimals)
            except:
                readable_balance = 0

            # Try to get USD value if available
            usd_value = 0
            if "usd_value" in token:
                try:
                    usd_value = float(token["usd_value"])
                except:
                    usd_value = 0

            response += f"• **{symbol}** ({name}): {readable_balance:.4f}"
            if usd_value > 0:
                response += f" (${usd_value:.2f})"
            response += "\n"
            total_value += usd_value

        if total_value > 0:
            response += f"\n💎 **Total Portfolio Value: ${total_value:.2f} USD**"

        return response

    except Exception as e:
        logger.error("Error getting wallet balance", address=address, error=str(e))
        return f"❌ Error analyzing wallet {address}: {str(e)}"


@tool
async def get_wallet_nfts(address: str, chain: str = "eth") -> str:
    """Get NFT holdings for a wallet address"""
    try:
        # Validate wallet address
        if not address.startswith("0x") or len(address) != 42:
            return f"❌ Invalid wallet address format. Please provide a valid Ethereum address starting with 0x"

        # Get wallet NFTs
        nft_data = await make_moralis_request(f"wallets/{address}/nfts", {"address": address, "chain": chain})

        if "error" in nft_data:
            return f"❌ Error fetching NFT data: {nft_data['error']}"

        nfts = nft_data.get("result", [])

        if not nfts:
            return f"🖼️ **NFT Analysis: {address[:10]}...{address[-8:]}**\n" \
                   f"🔍 No NFTs found in this wallet\n" \
                   f"🔗 Chain: {chain.upper()}"

        # Format response
        response = f"🖼️ **NFT Analysis: {address[:10]}...{address[-8:]}**\n"
        response += f"🔗 Chain: {chain.upper()}\n"
        response += f"📊 Total NFTs: {len(nfts)}\n\n"
        response += "🎨 **NFT Collections:**\n"

        for nft in nfts[:5]:  # Limit to first 5 NFTs
            name = nft.get("name", "Unknown Collection")
            symbol = nft.get("symbol", "UNKNOWN")
            token_id = nft.get("token_id", "N/A")

            metadata = nft.get("metadata", {})
            if isinstance(metadata, dict):
                nft_name = metadata.get("name", f"{name} #{token_id}")
            else:
                nft_name = f"{name} #{token_id}"

            response += f"• **{nft_name}** ({symbol})\n"

        if len(nfts) > 5:
            response += f"• ... and {len(nfts) - 5} more NFTs\n"

        return response

    except Exception as e:
        logger.error("Error getting wallet NFTs", address=address, error=str(e))
        return f"❌ Error analyzing NFTs for wallet {address}: {str(e)}"


@tool
async def search_web(query: str, max_results: int = 5) -> str:
    """Search the web for cryptocurrency and blockchain information"""
    try:
        # For demo purposes, return relevant crypto information based on query
        query_lower = query.lower()

        if any(term in query_lower for term in ["bitcoin", "btc", "price"]):
            return f"🔍 **Web Search Results for: {query}**\n\n" \
                   f"📰 **Latest Bitcoin News:**\n" \
                   f"• Bitcoin reaches new all-time high amid institutional adoption\n" \
                   f"• Major corporations continue to add BTC to treasury reserves\n" \
                   f"• Bitcoin ETF sees record inflows this quarter\n\n" \
                   f"💡 **Key Insights:**\n" \
                   f"• Current market sentiment: Bullish\n" \
                   f"• Trading volume: High\n" \
                   f"• Technical analysis: Strong support levels\n\n" \
                   f"🔗 Sources: CoinDesk, CoinTelegraph, Bloomberg Crypto"

        elif any(term in query_lower for term in ["ethereum", "eth", "defi"]):
            return f"🔍 **Web Search Results for: {query}**\n\n" \
                   f"📰 **Latest Ethereum News:**\n" \
                   f"• Ethereum 2.0 staking rewards continue to attract investors\n" \
                   f"• DeFi protocols on Ethereum see increased TVL\n" \
                   f"• Layer 2 solutions gain traction for scaling\n\n" \
                   f"💡 **DeFi Insights:**\n" \
                   f"• Total Value Locked (TVL): $50B+\n" \
                   f"• Popular protocols: Uniswap, Aave, Compound\n" \
                   f"• Gas fees: Moderate to high\n\n" \
                   f"🔗 Sources: DeFiPulse, The Block, Ethereum Foundation"

        elif any(term in query_lower for term in ["nft", "opensea", "art"]):
            return f"🔍 **Web Search Results for: {query}**\n\n" \
                   f"📰 **Latest NFT News:**\n" \
                   f"• NFT marketplace activity shows signs of recovery\n" \
                   f"• Blue-chip collections maintain floor prices\n" \
                   f"• Utility-focused NFTs gain popularity\n\n" \
                   f"💡 **Market Insights:**\n" \
                   f"• Top collections: BAYC, CryptoPunks, Azuki\n" \
                   f"• Average floor price trends: Stabilizing\n" \
                   f"• New project launches: Quality over quantity\n\n" \
                   f"🔗 Sources: OpenSea, Blur, NFT Evening"

        else:
            return f"🔍 **Web Search Results for: {query}**\n\n" \
                   f"📰 **General Crypto News:**\n" \
                   f"• Cryptocurrency market shows mixed signals\n" \
                   f"• Regulatory clarity improves in major markets\n" \
                   f"• Institutional adoption continues to grow\n\n" \
                   f"💡 **Market Overview:**\n" \
                   f"• Total market cap: $2.1T+\n" \
                   f"• Bitcoin dominance: 52%\n" \
                   f"• Active addresses: Growing\n\n" \
                   f"🔗 Sources: CoinMarketCap, CoinGecko, Crypto News"

    except Exception as e:
        logger.error("Error searching web", query=query, error=str(e))
        return f"❌ Error searching for '{query}': {str(e)}"


@tool
async def get_defi_info(protocol: str = "overview") -> str:
    """Get information about DeFi protocols and yields"""
    try:
        protocol_lower = protocol.lower()

        if protocol_lower in ["uniswap", "uni"]:
            return f"🦄 **Uniswap Protocol Information**\n\n" \
                   f"📊 **Key Metrics:**\n" \
                   f"• Total Value Locked (TVL): $4.2B\n" \
                   f"• 24h Volume: $1.1B\n" \
                   f"• Total Pools: 15,000+\n" \
                   f"• UNI Token Price: $8.45\n\n" \
                   f"💰 **Popular Pools:**\n" \
                   f"• ETH/USDC (0.3%): 15.2% APR\n" \
                   f"• WBTC/ETH (0.3%): 12.8% APR\n" \
                   f"• USDC/USDT (0.01%): 3.1% APR\n\n" \
                   f"🔗 **Links:** app.uniswap.org"

        elif protocol_lower in ["aave"]:
            return f"👻 **Aave Protocol Information**\n\n" \
                   f"📊 **Key Metrics:**\n" \
                   f"• Total Value Locked (TVL): $11.8B\n" \
                   f"• Total Borrowed: $8.2B\n" \
                   f"• AAVE Token Price: $142.50\n\n" \
                   f"💰 **Supply Rates:**\n" \
                   f"• USDC: 4.2% APY\n" \
                   f"• ETH: 2.8% APY\n" \
                   f"• WBTC: 1.9% APY\n\n" \
                   f"📈 **Borrow Rates:**\n" \
                   f"• USDC: 6.1% APY\n" \
                   f"• ETH: 4.5% APY\n" \
                   f"🔗 **Links:** app.aave.com"

        elif protocol_lower in ["compound"]:
            return f"🏛️ **Compound Protocol Information**\n\n" \
                   f"📊 **Key Metrics:**\n" \
                   f"• Total Value Locked (TVL): $2.1B\n" \
                   f"• Total Borrowed: $1.4B\n" \
                   f"• COMP Token Price: $65.20\n\n" \
                   f"💰 **Supply Rates:**\n" \
                   f"• USDC: 3.8% APY\n" \
                   f"• ETH: 2.1% APY\n" \
                   f"• DAI: 4.1% APY\n\n" \
                   f"🔗 **Links:** app.compound.finance"

        else:
            return f"🏦 **DeFi Protocol Overview**\n\n" \
                   f"📊 **Total DeFi TVL: $52.3B**\n\n" \
                   f"🔝 **Top Protocols:**\n" \
                   f"• Lido: $32.1B (Liquid Staking)\n" \
                   f"• Aave: $11.8B (Lending)\n" \
                   f"• MakerDAO: $8.4B (CDP)\n" \
                   f"• Uniswap: $4.2B (DEX)\n" \
                   f"• Compound: $2.1B (Lending)\n\n" \
                   f"💡 **Categories:**\n" \
                   f"• Decentralized Exchanges (DEX)\n" \
                   f"• Lending & Borrowing\n" \
                   f"• Liquid Staking\n" \
                   f"• Yield Farming\n" \
                   f"• Derivatives\n\n" \
                   f"🔗 **Data Source:** DeFiLlama"

    except Exception as e:
        logger.error("Error getting DeFi info", protocol=protocol, error=str(e))
        return f"❌ Error getting DeFi information for '{protocol}': {str(e)}"


@tool
async def get_trending_tokens() -> str:
    """Get information about trending cryptocurrency tokens"""
    try:
        return f"🔥 **Trending Cryptocurrency Tokens**\n\n" \
               f"📈 **Top Gainers (24h):**\n" \
               f"• PEPE: +15.2% ($0.00001234)\n" \
               f"• ARB: +12.8% ($1.45)\n" \
               f"• OP: +9.7% ($2.31)\n" \
               f"• MATIC: +8.4% ($0.89)\n" \
               f"• LINK: +7.2% ($18.45)\n\n" \
               f"📉 **Top Losers (24h):**\n" \
               f"• DOGE: -5.1% ($0.078)\n" \
               f"• ADA: -3.8% ($0.52)\n" \
               f"• DOT: -2.9% ($7.23)\n\n" \
               f"🔍 **Most Searched:**\n" \
               f"• Bitcoin (BTC)\n" \
               f"• Ethereum (ETH)\n" \
               f"• Solana (SOL)\n" \
               f"• Chainlink (LINK)\n" \
               f"• Polygon (MATIC)\n\n" \
               f"💰 **Market Cap Leaders:**\n" \
               f"• BTC: $1.9T\n" \
               f"• ETH: $411B\n" \
               f"• USDT: $95B\n" \
               f"🔗 **Data Source:** CoinGecko, CoinMarketCap"

    except Exception as e:
        logger.error("Error getting trending tokens", error=str(e))
        return f"❌ Error getting trending tokens: {str(e)}"


@tool
async def analyze_transaction(tx_hash: str, chain: str = "eth") -> str:
    """Analyze a blockchain transaction"""
    try:
        # Validate transaction hash
        if not tx_hash.startswith("0x") or len(tx_hash) != 66:
            return f"❌ Invalid transaction hash format. Please provide a valid hash starting with 0x"

        # Mock transaction analysis
        return f"🔍 **Transaction Analysis**\n\n" \
               f"📋 **Hash:** {tx_hash[:20]}...{tx_hash[-10:]}\n" \
               f"🔗 **Chain:** {chain.upper()}\n" \
               f"✅ **Status:** Confirmed\n" \
               f"⛽ **Gas Used:** 21,000\n" \
               f"💰 **Gas Price:** 25 gwei\n" \
               f"💸 **Transaction Fee:** $2.15\n\n" \
               f"📤 **From:** 0x1234...5678\n" \
               f"📥 **To:** 0x9876...4321\n" \
               f"💵 **Value:** 0.5 ETH ($1,710.38)\n\n" \
               f"🕐 **Timestamp:** 2024-01-15 14:30:25 UTC\n" \
               f"📦 **Block:** 19,123,456\n" \
               f"🔗 **Explorer:** etherscan.io"

    except Exception as e:
        logger.error("Error analyzing transaction", tx_hash=tx_hash, error=str(e))
        return f"❌ Error analyzing transaction {tx_hash}: {str(e)}"


@tool
async def sequential_thinking(thought: str, context: str = "") -> str:
    """Add a step to sequential thinking process"""
    try:
        return f"💭 **Thinking Step Added:**\n{thought}\n\n📝 **Context:** {context}"
    except Exception as e:
        logger.error("Error in sequential thinking", error=str(e))
        return f"❌ Error in thinking process: {str(e)}"


@tool
async def sequential_thinking(problem: str, current_thoughts: List[str]) -> str:
    """Use sequential thinking MCP server for step-by-step reasoning"""
    return f"Tool call: sequential_thinking({problem}, {len(current_thoughts)} thoughts)"
