"""
Additional API Endpoints for AP3X Crypto Agent Backend
Extended endpoints for blockchain data, social feeds, and wallet operations
"""

import uuid
from typing import Dict, Any, List, Optional

import structlog
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from services import AgentService
from core.config import get_settings

logger = structlog.get_logger()

# Create API router
router = APIRouter()


# Request/Response Models
class BlockchainQueryRequest(BaseModel):
    """Request model for blockchain queries"""
    address: str = Field(..., description="Wallet or contract address")
    chain: str = Field("eth", description="Blockchain network")
    query_type: str = Field(..., description="Type of query: balance, nfts, tokens, transactions")
    limit: Optional[int] = Field(10, description="Number of results to return")


class SocialFeedRequest(BaseModel):
    """Request model for social feed queries"""
    query: str = Field(..., description="Search query or crypto symbol")
    platform: str = Field("twitter", description="Social platform")
    max_results: int = Field(20, description="Maximum results to return")
    sentiment_filter: Optional[str] = Field(None, description="Filter by sentiment: positive, negative, neutral")


class WalletConnectionRequest(BaseModel):
    """Request model for wallet connection"""
    session_id: str = Field(..., description="Session ID")
    wallet_type: str = Field("phantom", description="Wallet type")


class TransactionRequest(BaseModel):
    """Request model for transactions"""
    session_id: str = Field(..., description="Session ID")
    to_address: str = Field(..., description="Recipient address")
    amount: float = Field(..., description="Amount to send")
    token: Optional[str] = Field(None, description="Token contract address (None for native token)")
    memo: Optional[str] = Field(None, description="Transaction memo")


class AnalyticsRequest(BaseModel):
    """Request model for analytics queries"""
    metric: str = Field(..., description="Metric to analyze")
    timeframe: str = Field("24h", description="Time frame: 1h, 24h, 7d, 30d")
    filters: Dict[str, Any] = Field(default_factory=dict, description="Additional filters")


# Dependency to get agent service
async def get_agent_service() -> AgentService:
    """Dependency to get the agent service"""
    from main import agent_service
    if agent_service is None:
        raise HTTPException(status_code=503, detail="Agent service not initialized")
    return agent_service


# Blockchain Data Endpoints
@router.post("/blockchain/query")
async def query_blockchain_data(
    request: BlockchainQueryRequest,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Query blockchain data through Moralis integration"""
    try:
        tool_registry = agent_svc.tool_registry
        moralis_client = tool_registry.get_tool("moralis")
        
        if not moralis_client:
            raise HTTPException(status_code=503, detail="Moralis service not available")
        
        logger.info("Processing blockchain query", 
                   address=request.address[:10] + "...", 
                   query_type=request.query_type)
        
        result = {}
        
        if request.query_type == "balance":
            wallet_info = await moralis_client.get_wallet_info(request.address, request.chain)
            result = {
                "address": request.address,
                "chain": request.chain,
                "balance": wallet_info.balance,
                "net_worth": wallet_info.net_worth
            }
        
        elif request.query_type == "nfts":
            nfts = await moralis_client.get_wallet_nfts(
                request.address, 
                chain=request.chain, 
                limit=request.limit
            )
            result = {
                "address": request.address,
                "chain": request.chain,
                "nfts": [nft.dict() for nft in nfts]
            }
        
        elif request.query_type == "tokens":
            tokens = await moralis_client.get_wallet_tokens(
                request.address, 
                chain=request.chain, 
                limit=request.limit
            )
            result = {
                "address": request.address,
                "chain": request.chain,
                "tokens": [token.dict() for token in tokens]
            }
        
        elif request.query_type == "transactions":
            # This would require additional implementation in moralis_client
            result = {
                "address": request.address,
                "chain": request.chain,
                "transactions": [],
                "message": "Transaction history not yet implemented"
            }
        
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported query type: {request.query_type}")
        
        return result
        
    except Exception as e:
        logger.error("Blockchain query failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/blockchain/price/{token_address}")
async def get_token_price(
    token_address: str,
    chain: str = Query("eth", description="Blockchain network"),
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Get token price"""
    try:
        tool_registry = agent_svc.tool_registry
        moralis_client = tool_registry.get_tool("moralis")
        
        if not moralis_client:
            raise HTTPException(status_code=503, detail="Moralis service not available")
        
        price_info = await moralis_client.get_token_price(token_address, chain)
        
        return {
            "token_address": token_address,
            "chain": chain,
            "price_info": price_info.dict() if price_info else None
        }
        
    except Exception as e:
        logger.error("Token price query failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Social Feed Endpoints
@router.post("/social/feed")
async def get_social_feed(
    request: SocialFeedRequest,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Get social media feed data"""
    try:
        tool_registry = agent_svc.tool_registry
        
        if request.platform == "twitter":
            twitter_client = tool_registry.get_tool("twitter")
            
            if not twitter_client:
                raise HTTPException(status_code=503, detail="Twitter service not available")
            
            # Search for crypto-related tweets
            tweets = await twitter_client.search_crypto_tweets(
                request.query,
                max_results=request.max_results,
                sentiment_filter=request.sentiment_filter
            )
            
            # Analyze sentiment
            sentiment_analysis = await twitter_client.analyze_sentiment(tweets)
            
            return {
                "platform": request.platform,
                "query": request.query,
                "tweets": [tweet.dict() for tweet in tweets],
                "sentiment_analysis": sentiment_analysis,
                "total_results": len(tweets)
            }
        
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported platform: {request.platform}")
        
    except Exception as e:
        logger.error("Social feed query failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/social/trending")
async def get_trending_topics(
    platform: str = Query("twitter", description="Social platform"),
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Get trending topics"""
    try:
        tool_registry = agent_svc.tool_registry
        
        if platform == "twitter":
            twitter_client = tool_registry.get_tool("twitter")
            
            if not twitter_client:
                raise HTTPException(status_code=503, detail="Twitter service not available")
            
            trending_topics = await twitter_client.get_trending_crypto_topics()
            
            return {
                "platform": platform,
                "trending_topics": trending_topics,
                "timestamp": agent_svc.session_manager._get_timestamp() if agent_svc.session_manager else None
            }
        
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported platform: {platform}")
        
    except Exception as e:
        logger.error("Trending topics query failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Wallet Integration Endpoints
@router.post("/wallet/connect")
async def connect_wallet(
    request: WalletConnectionRequest,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Initiate wallet connection"""
    try:
        tool_registry = agent_svc.tool_registry
        
        if request.wallet_type == "phantom":
            phantom_client = tool_registry.get_tool("phantom")
            
            if not phantom_client:
                raise HTTPException(status_code=503, detail="Phantom service not available")
            
            connection_url = phantom_client.generate_connection_url(request.session_id)
            
            return {
                "session_id": request.session_id,
                "wallet_type": request.wallet_type,
                "connection_url": connection_url,
                "status": "pending"
            }
        
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported wallet type: {request.wallet_type}")
        
    except Exception as e:
        logger.error("Wallet connection failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/wallet/status/{session_id}")
async def get_wallet_status(
    session_id: str,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Get wallet connection status"""
    try:
        tool_registry = agent_svc.tool_registry
        phantom_client = tool_registry.get_tool("phantom")
        
        if not phantom_client:
            raise HTTPException(status_code=503, detail="Phantom service not available")
        
        connection = phantom_client.get_connection(session_id)
        
        if connection:
            return {
                "session_id": session_id,
                "connected": True,
                "address": connection.address,
                "connected_at": connection.connected_at
            }
        else:
            return {
                "session_id": session_id,
                "connected": False
            }
        
    except Exception as e:
        logger.error("Wallet status query failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/wallet/transaction")
async def create_transaction(
    request: TransactionRequest,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Create a transaction"""
    try:
        tool_registry = agent_svc.tool_registry
        phantom_client = tool_registry.get_tool("phantom")
        
        if not phantom_client:
            raise HTTPException(status_code=503, detail="Phantom service not available")
        
        # Check if wallet is connected
        connection = phantom_client.get_connection(request.session_id)
        if not connection:
            raise HTTPException(status_code=400, detail="Wallet not connected")
        
        from phantom_client import TransactionRequest as PhantomTxRequest
        
        tx_request = PhantomTxRequest(
            to_address=request.to_address,
            amount=request.amount,
            token=request.token,
            memo=request.memo
        )
        
        transaction_url = phantom_client.generate_transaction_url(request.session_id, tx_request)
        
        return {
            "session_id": request.session_id,
            "transaction_url": transaction_url,
            "status": "pending_signature"
        }
        
    except Exception as e:
        logger.error("Transaction creation failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Analytics Endpoints
@router.post("/analytics/query")
async def query_analytics(
    request: AnalyticsRequest,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Query analytics data"""
    try:
        if request.metric == "agent_performance":
            metrics = await agent_svc.get_agent_metrics()
            return {
                "metric": request.metric,
                "timeframe": request.timeframe,
                "data": metrics
            }
        
        elif request.metric == "session_stats":
            sessions = await agent_svc.list_sessions(limit=100)
            return {
                "metric": request.metric,
                "timeframe": request.timeframe,
                "data": {
                    "total_sessions": len(sessions),
                    "active_sessions": len([s for s in sessions if s.get("status") == "active"]),
                    "sessions": sessions
                }
            }
        
        elif request.metric == "tool_health":
            tool_status = await agent_svc.tool_registry.check_services()
            return {
                "metric": request.metric,
                "timeframe": request.timeframe,
                "data": tool_status
            }
        
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported metric: {request.metric}")
        
    except Exception as e:
        logger.error("Analytics query failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Tool Management Endpoints
@router.get("/tools/status")
async def get_tools_status(agent_svc: AgentService = Depends(get_agent_service)):
    """Get status of all tools"""
    try:
        tool_status = await agent_svc.tool_registry.health_check_all()
        
        return {
            "tools": {name: status.dict() for name, status in tool_status.items()},
            "summary": {
                "total_tools": len(tool_status),
                "healthy_tools": len(agent_svc.tool_registry.list_healthy_tools()),
                "unhealthy_tools": len(agent_svc.tool_registry.list_unhealthy_tools())
            }
        }
        
    except Exception as e:
        logger.error("Tools status query failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tools/capabilities")
async def get_tools_capabilities(agent_svc: AgentService = Depends(get_agent_service)):
    """Get capabilities of all tools"""
    try:
        capabilities = await agent_svc.tool_registry.get_tool_capabilities()
        
        return {
            "capabilities": capabilities,
            "available_tools": agent_svc.tool_registry.list_tools()
        }
        
    except Exception as e:
        logger.error("Tools capabilities query failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
