"use client"

import type React from "react"
import { useState, useRef, useC<PERSON>back, useEffect } from "react"
import { useAP3X } from "@/hooks/use-ap3x"
import { But<PERSON> } from "@/components/ui/button"
import { MessageSquare, Settings, ArrowUp, User, Zap, Activity } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
}

export default function AP3XCryptoAgent() {
  const [chatInput, setChatInput] = useState("")
  const [isStreaming, setIsStreaming] = useState(false)
  
  // AP3X Platform integration
  const ap3x = useAP3X()
  
  // Use messages from the AP3X hook
  const messages = ap3x.messages

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const handleSendMessage = async () => {
    if (!chatInput.trim()) return

    const userPrompt = chatInput
    setChatInput("")
    setIsStreaming(true)

    try {
      // Send message using the AP3X hook
      await ap3x.sendMessage(userPrompt)
    } catch (error) {
      console.error("Error sending message:", error)
    } finally {
      setIsStreaming(false)
    }
  }

  return (
    <div className="min-h-screen bg-black text-white flex">
      {/* Sidebar */}
      <div className="w-80 bg-neutral-950 border-r border-neutral-800 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-neutral-800">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-blue-gradient rounded-lg flex items-center justify-center">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-white">AP3X Crypto Agent</h1>
              <p className="text-sm text-neutral-400">Blockchain Intelligence</p>
            </div>
          </div>
          
          {/* Connection Status */}
          <div className="flex items-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${ap3x.isBackendAvailable ? 'bg-green-400' : 'bg-red-400'}`} />
            <span className="text-neutral-400">
              {ap3x.isBackendAvailable ? 'Connected' : 'Disconnected'}
            </span>
            {ap3x.isConnected && (
              <span className="text-blue-400 ml-2">• WebSocket Active</span>
            )}
          </div>
        </div>

        {/* Session Info */}
        <div className="p-4 border-b border-neutral-800">
          <div className="text-xs text-neutral-500 mb-1">Session ID</div>
          <div className="text-sm text-neutral-300 font-mono">
            {ap3x.sessionId || 'Not connected'}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="p-4 space-y-2">
          <h3 className="text-sm font-medium text-white mb-3">Quick Actions</h3>
          <Button 
            variant="ghost" 
            className="w-full justify-start text-neutral-400 hover:text-white hover:bg-neutral-800"
            onClick={() => setChatInput("What's the current price of Bitcoin?")}
          >
            <Activity className="w-4 h-4 mr-2" />
            Check BTC Price
          </Button>
          <Button 
            variant="ghost" 
            className="w-full justify-start text-neutral-400 hover:text-white hover:bg-neutral-800"
            onClick={() => setChatInput("Analyze wallet 0x...")}
          >
            <User className="w-4 h-4 mr-2" />
            Analyze Wallet
          </Button>
          <Button
            variant="ghost"
            className="w-full justify-start text-neutral-400 hover:text-white hover:bg-neutral-800"
            onClick={() => setChatInput("Show trending crypto news")}
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            Crypto News
          </Button>
          <Button
            variant="ghost"
            className="w-full justify-start text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
            onClick={() => setChatInput("Hello, test connection")}
          >
            <Zap className="w-4 h-4 mr-2" />
            Test Connection
          </Button>
        </div>

        {/* Retry Connection */}
        {!ap3x.isBackendAvailable && (
          <div className="p-4 mt-auto">
            <Button 
              onClick={ap3x.retryConnection}
              disabled={ap3x.isLoading}
              className="w-full bg-blue-gradient-hover text-white"
            >
              {ap3x.isLoading ? 'Connecting...' : 'Retry Connection'}
            </Button>
          </div>
        )}
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-6 border-b border-neutral-800">
          <h2 className="text-lg font-medium text-white">Chat with AP3X Agent</h2>
          <p className="text-sm text-neutral-400">
            Ask about crypto prices, wallet analysis, DeFi protocols, and more
          </p>
        </div>

        {/* Messages */}
        <ScrollArea className="flex-1 p-6">
          <div className="space-y-6">
            {messages.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-blue-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-medium text-white mb-2">Welcome to AP3X Crypto Agent</h3>
                <p className="text-neutral-400 max-w-md mx-auto">
                  Start a conversation by asking about cryptocurrency prices, wallet analysis, 
                  DeFi protocols, or any blockchain-related questions.
                </p>
              </div>
            )}
            
            {messages.map((message) => (
              <div key={message.id} className="group">
                <div className={`flex gap-3 ${message.type === "user" ? "flex-row-reverse" : ""}`}>
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                      message.type === "user"
                        ? "bg-blue-600 text-white"
                        : "bg-neutral-800 text-neutral-400 border border-neutral-700"
                    }`}>
                      {message.type === "user" ? "U" : "AI"}
                    </div>
                  </div>
                  <div className={`flex-1 max-w-[85%] ${message.type === "user" ? "text-right" : ""}`}>
                    <div className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                      message.type === "user"
                        ? "bg-blue-600 text-white shadow-lg"
                        : "bg-neutral-900 text-neutral-100 shadow-md border border-neutral-800"
                    }`}>
                      <div className="whitespace-pre-wrap">{message.content}</div>
                    </div>
                    <div className="text-xs text-neutral-500 mt-1 px-1">
                      {formatTime(message.timestamp)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {isStreaming && (
              <div className="flex gap-3">
                <div className="w-8 h-8 rounded-full bg-neutral-800 border border-neutral-700 flex items-center justify-center">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                </div>
                <div className="bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3">
                  <div className="text-sm text-neutral-400">AP3X Agent is thinking...</div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Chat Input */}
        <div className="p-6 border-t border-neutral-800">
          <div className="relative bg-neutral-900 border border-neutral-800 rounded-lg p-1 focus-within:border-blue-500/40 transition-all">
            <textarea
              value={chatInput}
              onChange={(e) => {
                setChatInput(e.target.value)
                // Auto-resize textarea
                const textarea = e.target as HTMLTextAreaElement
                textarea.style.height = "auto"
                const scrollHeight = textarea.scrollHeight
                const maxHeight = 120 // 5 lines max
                textarea.style.height = Math.min(scrollHeight, maxHeight) + "px"
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
                  e.preventDefault()
                  handleSendMessage()
                }
              }}
              placeholder="Ask about crypto prices, wallet analysis, DeFi protocols... (Ctrl+Enter to send)"
              className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-neutral-500 resize-none focus:outline-none leading-6 scrollbar-none overflow-y-auto"
              rows={2}
              style={{
                minHeight: "60px",
                maxHeight: "120px",
              }}
            />
            <button
              onClick={handleSendMessage}
              disabled={!chatInput.trim() || isStreaming}
              className="absolute right-2 top-3 w-8 h-8 bg-blue-600 hover:bg-blue-700 disabled:bg-neutral-700 disabled:text-neutral-500 text-white rounded-md flex items-center justify-center transition-colors"
            >
              <ArrowUp className="w-4 h-4" />
            </button>
          </div>
          <div className="flex items-center justify-between mt-2 text-xs text-neutral-500">
            <div>
              {ap3x.isBackendAvailable && <span>✓ Backend connected</span>}
              {ap3x.isConnected && <span className="ml-2">• WebSocket active</span>}
            </div>
            <div>Ctrl+Enter to send</div>
          </div>
        </div>
      </div>
    </div>
  )
}
