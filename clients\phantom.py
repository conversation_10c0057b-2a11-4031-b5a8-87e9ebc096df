"""
Phantom Wallet Client for AP3X Crypto Agent Backend
Provides wallet connection and transaction capabilities
"""

import json
import secrets
import time
from typing import Dict, Any, List, Optional
from urllib.parse import urlencode, parse_qs

import httpx
import structlog
from pydantic import BaseModel, Field

logger = structlog.get_logger()


class WalletConnection(BaseModel):
    """Wallet connection model"""
    address: str
    public_key: str
    connected_at: float
    app_id: str
    session_id: str


class TransactionRequest(BaseModel):
    """Transaction request model"""
    to_address: str
    amount: float
    token: Optional[str] = None  # None for SOL, token mint address for SPL tokens
    memo: Optional[str] = None


class TransactionResult(BaseModel):
    """Transaction result model"""
    signature: str
    status: str
    block_time: Optional[int] = None
    fee: Optional[int] = None
    error: Optional[str] = None


class PhantomClient:
    """Phantom wallet integration client"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.app_id = config.get("app_id")
        self.redirect_url = config.get("redirect_url", "http://localhost:8000/phantom/callback")
        self.cluster = config.get("cluster", "mainnet-beta")  # mainnet-beta, testnet, devnet
        
        # Phantom deep link URLs
        self.phantom_base_url = "https://phantom.app/ul/v1"
        
        # Active connections storage (in production, use Redis or database)
        self.active_connections: Dict[str, WalletConnection] = {}
        
        # Pending transactions
        self.pending_transactions: Dict[str, Dict[str, Any]] = {}
    
    def generate_connection_url(self, session_id: str) -> str:
        """Generate Phantom wallet connection URL"""
        # Generate a random nonce for security
        nonce = secrets.token_urlsafe(32)
        
        # Store session info for callback verification
        self.pending_transactions[nonce] = {
            "session_id": session_id,
            "type": "connect",
            "created_at": time.time()
        }
        
        # Build connection parameters
        params = {
            "dapp_encryption_public_key": self._generate_encryption_key(),
            "cluster": self.cluster,
            "app_url": self.redirect_url,
            "redirect_link": f"{self.redirect_url}?nonce={nonce}"
        }
        
        connection_url = f"{self.phantom_base_url}/connect?" + urlencode(params)
        
        logger.info("Generated Phantom connection URL", session_id=session_id, nonce=nonce)
        return connection_url
    
    def handle_connection_callback(self, callback_data: Dict[str, Any]) -> Optional[WalletConnection]:
        """Handle connection callback from Phantom"""
        try:
            # Extract data from callback
            nonce = callback_data.get("nonce")
            public_key = callback_data.get("phantom_encryption_public_key")
            data = callback_data.get("data")
            
            if not nonce or nonce not in self.pending_transactions:
                logger.error("Invalid or expired nonce", nonce=nonce)
                return None
            
            pending = self.pending_transactions.pop(nonce)
            session_id = pending["session_id"]
            
            # Decrypt and parse connection data
            if data:
                # In a real implementation, you would decrypt the data here
                # For now, we'll assume the public key is the wallet address
                wallet_address = public_key  # Simplified
                
                connection = WalletConnection(
                    address=wallet_address,
                    public_key=public_key,
                    connected_at=time.time(),
                    app_id=self.app_id,
                    session_id=session_id
                )
                
                self.active_connections[session_id] = connection
                
                logger.info("Wallet connected successfully", 
                           session_id=session_id, 
                           address=wallet_address[:10] + "...")
                
                return connection
            
            return None
            
        except Exception as e:
            logger.error("Failed to handle connection callback", error=str(e))
            return None
    
    def get_connection(self, session_id: str) -> Optional[WalletConnection]:
        """Get active wallet connection for session"""
        return self.active_connections.get(session_id)
    
    def disconnect_wallet(self, session_id: str) -> bool:
        """Disconnect wallet for session"""
        if session_id in self.active_connections:
            connection = self.active_connections.pop(session_id)
            logger.info("Wallet disconnected", 
                       session_id=session_id, 
                       address=connection.address[:10] + "...")
            return True
        return False
    
    def generate_transaction_url(
        self,
        session_id: str,
        transaction_request: TransactionRequest
    ) -> Optional[str]:
        """Generate transaction URL for Phantom"""
        connection = self.get_connection(session_id)
        if not connection:
            logger.error("No wallet connection found", session_id=session_id)
            return None
        
        try:
            # Generate transaction nonce
            nonce = secrets.token_urlsafe(32)
            
            # Store transaction info
            self.pending_transactions[nonce] = {
                "session_id": session_id,
                "type": "transaction",
                "transaction_request": transaction_request.dict(),
                "created_at": time.time()
            }
            
            # Build transaction parameters
            params = {
                "dapp_encryption_public_key": self._generate_encryption_key(),
                "nonce": nonce,
                "redirect_link": f"{self.redirect_url}?nonce={nonce}&type=transaction"
            }
            
            # Add transaction-specific parameters
            if transaction_request.token:
                # SPL token transfer
                params["transaction"] = self._build_spl_transaction(transaction_request)
            else:
                # SOL transfer
                params["transaction"] = self._build_sol_transaction(transaction_request)
            
            transaction_url = f"{self.phantom_base_url}/signAndSendTransaction?" + urlencode(params)
            
            logger.info("Generated transaction URL", 
                       session_id=session_id, 
                       to_address=transaction_request.to_address[:10] + "...")
            
            return transaction_url
            
        except Exception as e:
            logger.error("Failed to generate transaction URL", 
                        session_id=session_id, 
                        error=str(e))
            return None
    
    def handle_transaction_callback(self, callback_data: Dict[str, Any]) -> Optional[TransactionResult]:
        """Handle transaction callback from Phantom"""
        try:
            nonce = callback_data.get("nonce")
            signature = callback_data.get("signature")
            error_code = callback_data.get("errorCode")
            error_message = callback_data.get("errorMessage")
            
            if not nonce or nonce not in self.pending_transactions:
                logger.error("Invalid or expired transaction nonce", nonce=nonce)
                return None
            
            pending = self.pending_transactions.pop(nonce)
            session_id = pending["session_id"]
            
            if error_code or error_message:
                result = TransactionResult(
                    signature="",
                    status="failed",
                    error=f"Error {error_code}: {error_message}" if error_code else error_message
                )
                
                logger.error("Transaction failed", 
                           session_id=session_id, 
                           error=result.error)
            else:
                result = TransactionResult(
                    signature=signature,
                    status="pending"  # Will be confirmed later
                )
                
                logger.info("Transaction submitted", 
                           session_id=session_id, 
                           signature=signature)
            
            return result
            
        except Exception as e:
            logger.error("Failed to handle transaction callback", error=str(e))
            return None
    
    async def get_transaction_status(self, signature: str) -> Optional[TransactionResult]:
        """Get transaction status from Solana RPC"""
        try:
            # Use Solana RPC to check transaction status
            rpc_url = self._get_rpc_url()
            
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getSignatureStatuses",
                "params": [[signature], {"searchTransactionHistory": True}]
            }
            
            async with httpx.AsyncClient(timeout=30) as client:
                response = await client.post(rpc_url, json=payload)
                response.raise_for_status()
                data = response.json()
            
            if "result" in data and data["result"]["value"]:
                status_info = data["result"]["value"][0]
                
                if status_info is None:
                    return TransactionResult(
                        signature=signature,
                        status="not_found"
                    )
                
                if status_info.get("err"):
                    return TransactionResult(
                        signature=signature,
                        status="failed",
                        error=str(status_info["err"])
                    )
                
                return TransactionResult(
                    signature=signature,
                    status="confirmed",
                    block_time=status_info.get("slot"),
                    fee=None  # Would need additional RPC call to get fee
                )
            
            return None
            
        except Exception as e:
            logger.error("Failed to get transaction status", signature=signature, error=str(e))
            return None
    
    def _generate_encryption_key(self) -> str:
        """Generate encryption key for Phantom communication"""
        # In a real implementation, you would generate a proper encryption key
        # For now, return a placeholder
        return secrets.token_urlsafe(32)
    
    def _build_sol_transaction(self, request: TransactionRequest) -> str:
        """Build SOL transfer transaction"""
        # This is a simplified version - in reality you'd build a proper Solana transaction
        transaction_data = {
            "type": "transfer",
            "to": request.to_address,
            "amount": int(request.amount * 1e9),  # Convert SOL to lamports
            "memo": request.memo
        }
        return json.dumps(transaction_data)
    
    def _build_spl_transaction(self, request: TransactionRequest) -> str:
        """Build SPL token transfer transaction"""
        # This is a simplified version - in reality you'd build a proper SPL token transaction
        transaction_data = {
            "type": "spl_transfer",
            "to": request.to_address,
            "token_mint": request.token,
            "amount": request.amount,
            "memo": request.memo
        }
        return json.dumps(transaction_data)
    
    def _get_rpc_url(self) -> str:
        """Get Solana RPC URL based on cluster"""
        if self.cluster == "mainnet-beta":
            return "https://api.mainnet-beta.solana.com"
        elif self.cluster == "testnet":
            return "https://api.testnet.solana.com"
        elif self.cluster == "devnet":
            return "https://api.devnet.solana.com"
        else:
            return "https://api.mainnet-beta.solana.com"
    
    def list_active_connections(self) -> List[Dict[str, Any]]:
        """List all active wallet connections"""
        connections = []
        for session_id, connection in self.active_connections.items():
            connections.append({
                "session_id": session_id,
                "address": connection.address,
                "connected_at": connection.connected_at,
                "app_id": connection.app_id
            })
        return connections
    
    def cleanup_expired_connections(self, max_age_hours: int = 24):
        """Cleanup expired connections"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        expired_sessions = []
        for session_id, connection in self.active_connections.items():
            if current_time - connection.connected_at > max_age_seconds:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.disconnect_wallet(session_id)
        
        logger.info("Cleaned up expired wallet connections", count=len(expired_sessions))
        return len(expired_sessions)
