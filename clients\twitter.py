"""
Twitter Client for AP3X Crypto Agent Backend
Provides access to Twitter API for social feeds and sentiment analysis
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

import httpx
import structlog
from pydantic import BaseModel, Field

logger = structlog.get_logger()


class Tweet(BaseModel):
    """Tweet model"""
    id: str
    text: str
    author_id: str
    author_username: Optional[str] = None
    author_name: Optional[str] = None
    created_at: datetime
    public_metrics: Dict[str, int] = Field(default_factory=dict)
    context_annotations: List[Dict[str, Any]] = Field(default_factory=list)
    entities: Dict[str, Any] = Field(default_factory=dict)
    referenced_tweets: List[Dict[str, Any]] = Field(default_factory=list)


class TwitterUser(BaseModel):
    """Twitter user model"""
    id: str
    username: str
    name: str
    description: Optional[str] = None
    public_metrics: Dict[str, int] = Field(default_factory=dict)
    verified: bool = False
    created_at: Optional[datetime] = None
    profile_image_url: Optional[str] = None


class TrendingTopic(BaseModel):
    """Trending topic model"""
    name: str
    query: str
    tweet_volume: Optional[int] = None
    url: Optional[str] = None


class TwitterClient:
    """Twitter API client for social feeds integration"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.bearer_token = config.get("bearer_token")
        self.api_key = config.get("api_key")
        self.api_secret = config.get("api_secret")
        self.access_token = config.get("access_token")
        self.access_token_secret = config.get("access_token_secret")
        self.timeout = config.get("timeout", 30)
        
        # Twitter API v2 base URL
        self.base_url = "https://api.twitter.com/2"
        
        # Common headers
        self.headers = {
            "Authorization": f"Bearer {self.bearer_token}",
            "Content-Type": "application/json"
        }
    
    async def test_connection(self) -> bool:
        """Test Twitter API connection"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/users/me",
                    headers=self.headers
                )
                response.raise_for_status()
                return True
        except Exception as e:
            logger.error("Twitter connection test failed", error=str(e))
            return False
    
    async def search_tweets(
        self,
        query: str,
        max_results: int = 10,
        tweet_fields: Optional[List[str]] = None,
        user_fields: Optional[List[str]] = None,
        expansions: Optional[List[str]] = None
    ) -> List[Tweet]:
        """Search for tweets"""
        try:
            # Default fields to include
            if not tweet_fields:
                tweet_fields = [
                    "id", "text", "author_id", "created_at", "public_metrics",
                    "context_annotations", "entities", "referenced_tweets"
                ]
            
            if not user_fields:
                user_fields = ["id", "username", "name", "verified", "public_metrics"]
            
            if not expansions:
                expansions = ["author_id"]
            
            params = {
                "query": query,
                "max_results": min(max_results, 100),  # API limit
                "tweet.fields": ",".join(tweet_fields),
                "user.fields": ",".join(user_fields),
                "expansions": ",".join(expansions)
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/tweets/search/recent",
                    headers=self.headers,
                    params=params
                )
                response.raise_for_status()
                data = response.json()
            
            tweets = []
            users_map = {}
            
            # Create users map for quick lookup
            if "includes" in data and "users" in data["includes"]:
                for user in data["includes"]["users"]:
                    users_map[user["id"]] = user
            
            # Process tweets
            for tweet_data in data.get("data", []):
                # Get author info
                author_id = tweet_data.get("author_id")
                author_info = users_map.get(author_id, {})
                
                tweet = Tweet(
                    id=tweet_data["id"],
                    text=tweet_data["text"],
                    author_id=author_id,
                    author_username=author_info.get("username"),
                    author_name=author_info.get("name"),
                    created_at=datetime.fromisoformat(tweet_data["created_at"].replace("Z", "+00:00")),
                    public_metrics=tweet_data.get("public_metrics", {}),
                    context_annotations=tweet_data.get("context_annotations", []),
                    entities=tweet_data.get("entities", {}),
                    referenced_tweets=tweet_data.get("referenced_tweets", [])
                )
                tweets.append(tweet)
            
            logger.info("Twitter search completed", query=query, results=len(tweets))
            return tweets
            
        except Exception as e:
            logger.error("Twitter search failed", query=query, error=str(e))
            return []
    
    async def get_user_tweets(
        self,
        user_id: str,
        max_results: int = 10,
        exclude_replies: bool = True,
        exclude_retweets: bool = True
    ) -> List[Tweet]:
        """Get tweets from a specific user"""
        try:
            params = {
                "max_results": min(max_results, 100),
                "tweet.fields": "id,text,author_id,created_at,public_metrics,context_annotations",
                "exclude": []
            }
            
            if exclude_replies:
                params["exclude"].append("replies")
            if exclude_retweets:
                params["exclude"].append("retweets")
            
            if params["exclude"]:
                params["exclude"] = ",".join(params["exclude"])
            else:
                del params["exclude"]
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/users/{user_id}/tweets",
                    headers=self.headers,
                    params=params
                )
                response.raise_for_status()
                data = response.json()
            
            tweets = []
            for tweet_data in data.get("data", []):
                tweet = Tweet(
                    id=tweet_data["id"],
                    text=tweet_data["text"],
                    author_id=tweet_data["author_id"],
                    created_at=datetime.fromisoformat(tweet_data["created_at"].replace("Z", "+00:00")),
                    public_metrics=tweet_data.get("public_metrics", {}),
                    context_annotations=tweet_data.get("context_annotations", [])
                )
                tweets.append(tweet)
            
            logger.info("User tweets retrieved", user_id=user_id, results=len(tweets))
            return tweets
            
        except Exception as e:
            logger.error("Failed to get user tweets", user_id=user_id, error=str(e))
            return []
    
    async def get_user_info(self, username: str) -> Optional[TwitterUser]:
        """Get user information by username"""
        try:
            params = {
                "user.fields": "id,username,name,description,public_metrics,verified,created_at,profile_image_url"
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/users/by/username/{username}",
                    headers=self.headers,
                    params=params
                )
                response.raise_for_status()
                data = response.json()
            
            user_data = data.get("data")
            if not user_data:
                return None
            
            created_at = None
            if user_data.get("created_at"):
                created_at = datetime.fromisoformat(user_data["created_at"].replace("Z", "+00:00"))
            
            user = TwitterUser(
                id=user_data["id"],
                username=user_data["username"],
                name=user_data["name"],
                description=user_data.get("description"),
                public_metrics=user_data.get("public_metrics", {}),
                verified=user_data.get("verified", False),
                created_at=created_at,
                profile_image_url=user_data.get("profile_image_url")
            )
            
            logger.info("User info retrieved", username=username)
            return user
            
        except Exception as e:
            logger.error("Failed to get user info", username=username, error=str(e))
            return None
    
    async def search_crypto_tweets(
        self,
        crypto_symbol: str,
        max_results: int = 20,
        sentiment_filter: Optional[str] = None
    ) -> List[Tweet]:
        """Search for crypto-related tweets"""
        # Build query for crypto-specific search
        query_parts = [f"${crypto_symbol}", f"#{crypto_symbol}", crypto_symbol]
        
        # Add crypto-related keywords
        crypto_keywords = ["crypto", "cryptocurrency", "blockchain", "DeFi", "NFT", "trading"]
        query = f"({' OR '.join(query_parts)}) ({' OR '.join(crypto_keywords)})"
        
        # Add sentiment filter if specified
        if sentiment_filter == "positive":
            query += " (bullish OR moon OR pump OR gains OR profit)"
        elif sentiment_filter == "negative":
            query += " (bearish OR dump OR crash OR loss OR sell)"
        
        # Exclude retweets and replies for cleaner results
        query += " -is:retweet -is:reply"
        
        return await self.search_tweets(query, max_results=max_results)
    
    async def get_trending_crypto_topics(self) -> List[str]:
        """Get trending crypto-related topics"""
        try:
            # Search for trending crypto hashtags
            crypto_queries = [
                "#Bitcoin", "#Ethereum", "#DeFi", "#NFT", "#crypto",
                "#blockchain", "#altcoin", "#trading", "#HODL"
            ]
            
            trending_topics = []
            
            for query in crypto_queries:
                tweets = await self.search_tweets(query, max_results=5)
                if tweets:
                    # Extract hashtags from tweets
                    for tweet in tweets:
                        entities = tweet.entities
                        if "hashtags" in entities:
                            for hashtag in entities["hashtags"]:
                                tag = hashtag.get("tag", "").lower()
                                if tag and tag not in trending_topics:
                                    trending_topics.append(f"#{tag}")
            
            return trending_topics[:20]  # Return top 20
            
        except Exception as e:
            logger.error("Failed to get trending crypto topics", error=str(e))
            return []
    
    async def analyze_sentiment(self, tweets: List[Tweet]) -> Dict[str, Any]:
        """Analyze sentiment of tweets (basic implementation)"""
        if not tweets:
            return {"sentiment": "neutral", "confidence": 0.0, "analysis": {}}
        
        # Simple keyword-based sentiment analysis
        positive_keywords = [
            "bullish", "moon", "pump", "gains", "profit", "buy", "hold",
            "amazing", "great", "excellent", "love", "best", "up", "rise"
        ]
        
        negative_keywords = [
            "bearish", "dump", "crash", "loss", "sell", "terrible",
            "awful", "hate", "worst", "down", "fall", "drop"
        ]
        
        positive_count = 0
        negative_count = 0
        total_tweets = len(tweets)
        
        for tweet in tweets:
            text = tweet.text.lower()
            
            # Count positive keywords
            positive_count += sum(1 for keyword in positive_keywords if keyword in text)
            
            # Count negative keywords
            negative_count += sum(1 for keyword in negative_keywords if keyword in text)
        
        # Determine overall sentiment
        if positive_count > negative_count:
            sentiment = "positive"
            confidence = min(positive_count / (positive_count + negative_count), 1.0)
        elif negative_count > positive_count:
            sentiment = "negative"
            confidence = min(negative_count / (positive_count + negative_count), 1.0)
        else:
            sentiment = "neutral"
            confidence = 0.5
        
        return {
            "sentiment": sentiment,
            "confidence": confidence,
            "analysis": {
                "total_tweets": total_tweets,
                "positive_signals": positive_count,
                "negative_signals": negative_count,
                "neutral_tweets": total_tweets - (positive_count + negative_count)
            }
        }
