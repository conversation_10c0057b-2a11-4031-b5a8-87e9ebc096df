{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {"NODE_ENV": "production"}}, "moralis": {"command": "npx", "args": ["@moralisweb3/api-mcp-server", "--transport", "stdio"], "env": {"MORALIS_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImI1YmVhMTc1LWU5MmEtNGU0ZS1hOTgxLWU3ZjJlNTk5NjYyMCIsIm9yZ0lkIjoiNDA1NzgzIiwidXNlcklkIjoiNDE2OTY2IiwidHlwZUlkIjoiZTYyZmNjOGMtMTZlMi00ZmY1LWJjMjItNzVlYTBhZTRjNDk2IiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3MjQ1MTYzOTcsImV4cCI6NDg4MDI3NjM5N30.73TV_J52D11itXTgHvc1wr_kavMbyHAcxCya1TNEoeI"}}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "tavily": {"command": "uvx", "args": ["mcp-server-tavily"], "env": {"TAVILY_API_KEY": "${TAVILY_API_KEY}"}}}}