"""
Configuration management for AP3X Crypto Agent Backend
Handles all environment variables and settings
"""

import os
from functools import lru_cache
from typing import Optional, List
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application settings
    app_name: str = "AP3X Crypto Agent Backend"
    version: str = "1.0.0"
    debug: bool = Field(False, env="DEBUG")
    host: str = Field("0.0.0.0", env="HOST")
    port: int = Field(8000, env="PORT")
    
    # Security settings
    secret_key: str = Field(..., env="SECRET_KEY")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # CORS settings
    allowed_origins: List[str] = Field(["*"], env="ALLOWED_ORIGINS")
    
    # OpenRouter/LLM Configuration
    openrouter_api_key: str = Field(..., env="OPENROUTER_API_KEY")
    openrouter_model: str = Field("moonshotai/kimi-k2", env="OPENROUTER_MODEL")
    openrouter_base_url: str = Field("https://openrouter.ai/api/v1", env="OPENROUTER_BASE_URL")
    
    # Moralis Configuration
    moralis_api_key: str = Field(..., env="MORALIS_API_KEY")
    moralis_mcp_server_url: str = Field("http://localhost:3001", env="MORALIS_MCP_SERVER_URL")
    
    # Web Research Configuration
    tavily_api_key: Optional[str] = Field(None, env="TAVILY_API_KEY")
    brave_api_key: Optional[str] = Field(None, env="BRAVE_API_KEY")
    google_api_key: Optional[str] = Field(None, env="GOOGLE_API_KEY")
    google_cse_id: Optional[str] = Field(None, env="GOOGLE_CSE_ID")
    
    # MCP Server URLs
    sequential_thinking_mcp_url: str = Field("http://localhost:3000", env="SEQUENTIAL_THINKING_MCP_URL")
    fetch_mcp_server_url: str = Field("http://localhost:3002", env="FETCH_MCP_SERVER_URL")
    tavily_mcp_server_url: str = Field("http://localhost:3003", env="TAVILY_MCP_SERVER_URL")
    
    # Social Media Configuration
    twitter_api_key: Optional[str] = Field(None, env="TWITTER_API_KEY")
    twitter_api_secret: Optional[str] = Field(None, env="TWITTER_API_SECRET")
    twitter_access_token: Optional[str] = Field(None, env="TWITTER_ACCESS_TOKEN")
    twitter_access_token_secret: Optional[str] = Field(None, env="TWITTER_ACCESS_TOKEN_SECRET")
    twitter_bearer_token: Optional[str] = Field(None, env="TWITTER_BEARER_TOKEN")
    
    # Wallet Integration Configuration
    phantom_app_id: Optional[str] = Field(None, env="PHANTOM_APP_ID")
    phantom_redirect_url: Optional[str] = Field(None, env="PHANTOM_REDIRECT_URL")
    
    # Redis Configuration (for session management)
    redis_url: str = Field("redis://localhost:6379", env="REDIS_URL")
    redis_password: Optional[str] = Field(None, env="REDIS_PASSWORD")
    redis_db: int = Field(0, env="REDIS_DB")
    
    # Session Configuration
    session_timeout_minutes: int = Field(60, env="SESSION_TIMEOUT_MINUTES")
    max_sessions_per_user: int = Field(10, env="MAX_SESSIONS_PER_USER")
    
    # Agent Configuration
    agent_max_thinking_steps: int = Field(10, env="AGENT_MAX_THINKING_STEPS")
    agent_confidence_threshold: float = Field(0.7, env="AGENT_CONFIDENCE_THRESHOLD")
    agent_timeout_seconds: int = Field(300, env="AGENT_TIMEOUT_SECONDS")
    
    # Blockchain Configuration
    default_chain: str = Field("eth", env="DEFAULT_CHAIN")
    supported_chains: List[str] = Field(
        ["eth", "polygon", "bsc", "arbitrum", "optimism", "base"],
        env="SUPPORTED_CHAINS"
    )
    
    # Rate Limiting
    rate_limit_requests_per_minute: int = Field(60, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    rate_limit_burst: int = Field(10, env="RATE_LIMIT_BURST")
    
    # Logging Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_format: str = Field("json", env="LOG_FORMAT")  # json or text
    
    # External Service Timeouts
    moralis_timeout_seconds: int = Field(30, env="MORALIS_TIMEOUT_SECONDS")
    web_research_timeout_seconds: int = Field(60, env="WEB_RESEARCH_TIMEOUT_SECONDS")
    twitter_timeout_seconds: int = Field(30, env="TWITTER_TIMEOUT_SECONDS")
    
    @validator("allowed_origins", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("supported_chains", pre=True)
    def parse_supported_chains(cls, v):
        """Parse supported chains from string or list"""
        if isinstance(v, str):
            return [chain.strip() for chain in v.split(",")]
        return v
    
    @validator("secret_key")
    def validate_secret_key(cls, v):
        """Validate secret key is provided"""
        if not v or len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    @validator("openrouter_api_key")
    def validate_openrouter_key(cls, v):
        """Validate OpenRouter API key is provided"""
        if not v:
            raise ValueError("OPENROUTER_API_KEY is required")
        return v
    
    @validator("moralis_api_key")
    def validate_moralis_key(cls, v):
        """Validate Moralis API key is provided"""
        if not v:
            raise ValueError("MORALIS_API_KEY is required")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


class DevelopmentSettings(Settings):
    """Development-specific settings"""
    debug: bool = True
    log_level: str = "DEBUG"
    allowed_origins: List[str] = ["http://localhost:3000", "http://localhost:3001"]


class ProductionSettings(Settings):
    """Production-specific settings"""
    debug: bool = False
    log_level: str = "INFO"
    # Override with specific production origins
    allowed_origins: List[str] = []  # Should be set via environment


class TestingSettings(Settings):
    """Testing-specific settings"""
    debug: bool = True
    log_level: str = "DEBUG"
    redis_url: str = "redis://localhost:6379/1"  # Use different DB for tests
    
    # Use mock values for testing
    openrouter_api_key: str = "test-key"
    moralis_api_key: str = "test-key"
    secret_key: str = "test-secret-key-that-is-long-enough-for-validation"


@lru_cache()
def get_settings() -> Settings:
    """Get application settings based on environment"""
    environment = os.getenv("ENVIRONMENT", "development").lower()
    
    if environment == "production":
        return ProductionSettings()
    elif environment == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# Export commonly used settings
def get_redis_config() -> dict:
    """Get Redis configuration"""
    settings = get_settings()
    return {
        "url": settings.redis_url,
        "password": settings.redis_password,
        "db": settings.redis_db,
        "decode_responses": True,
        "socket_timeout": 5,
        "socket_connect_timeout": 5,
        "retry_on_timeout": True,
    }


def get_llm_config() -> dict:
    """Get LLM configuration"""
    settings = get_settings()
    return {
        "model": settings.openrouter_model,
        "api_key": settings.openrouter_api_key,
        "base_url": settings.openrouter_base_url,
        "timeout": settings.agent_timeout_seconds,
    }


def get_moralis_config() -> dict:
    """Get Moralis configuration"""
    settings = get_settings()
    return {
        "api_key": settings.moralis_api_key,
        "mcp_server_url": settings.moralis_mcp_server_url,
        "timeout": settings.moralis_timeout_seconds,
        "default_chain": settings.default_chain,
        "supported_chains": settings.supported_chains,
    }


def get_web_research_config() -> dict:
    """Get web research configuration"""
    settings = get_settings()
    return {
        "tavily_api_key": settings.tavily_api_key,
        "brave_api_key": settings.brave_api_key,
        "google_api_key": settings.google_api_key,
        "google_cse_id": settings.google_cse_id,
        "fetch_server_url": settings.fetch_mcp_server_url,
        "tavily_server_url": settings.tavily_mcp_server_url,
        "timeout": settings.web_research_timeout_seconds,
    }


def get_twitter_config() -> dict:
    """Get Twitter configuration"""
    settings = get_settings()
    return {
        "api_key": settings.twitter_api_key,
        "api_secret": settings.twitter_api_secret,
        "access_token": settings.twitter_access_token,
        "access_token_secret": settings.twitter_access_token_secret,
        "bearer_token": settings.twitter_bearer_token,
        "timeout": settings.twitter_timeout_seconds,
    }


def get_phantom_config() -> dict:
    """Get Phantom wallet configuration"""
    settings = get_settings()
    return {
        "app_id": settings.phantom_app_id,
        "redirect_url": settings.phantom_redirect_url,
    }
