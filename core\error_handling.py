"""
Error Handling and Monitoring for AP3X Crypto Agent Backend
Comprehensive error handling, logging, and monitoring system
"""

import asyncio
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from functools import wraps

import structlog
from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
from pydantic import BaseModel

logger = structlog.get_logger()


class ErrorInfo(BaseModel):
    """Error information model"""
    error_id: str
    error_type: str
    message: str
    timestamp: str
    context: Dict[str, Any] = {}
    stack_trace: Optional[str] = None
    user_message: Optional[str] = None


class ErrorMetrics(BaseModel):
    """Error metrics model"""
    total_errors: int = 0
    errors_by_type: Dict[str, int] = {}
    errors_by_endpoint: Dict[str, int] = {}
    recent_errors: List[ErrorInfo] = []
    error_rate_per_hour: float = 0.0


class ErrorHandler:
    """Centralized error handling and monitoring"""
    
    def __init__(self, max_recent_errors: int = 100):
        self.max_recent_errors = max_recent_errors
        self.error_metrics = ErrorMetrics()
        self.error_callbacks: List[Callable] = []
    
    def add_error_callback(self, callback: Callable):
        """Add callback to be called when errors occur"""
        self.error_callbacks.append(callback)
    
    async def handle_error(
        self,
        error: Exception,
        context: Dict[str, Any] = None,
        user_message: str = None,
        error_id: str = None
    ) -> ErrorInfo:
        """Handle and log an error"""
        import uuid
        
        if error_id is None:
            error_id = str(uuid.uuid4())
        
        error_info = ErrorInfo(
            error_id=error_id,
            error_type=type(error).__name__,
            message=str(error),
            timestamp=datetime.utcnow().isoformat(),
            context=context or {},
            stack_trace=traceback.format_exc(),
            user_message=user_message
        )
        
        # Update metrics
        self._update_metrics(error_info)
        
        # Log the error
        logger.error(
            "Error occurred",
            error_id=error_id,
            error_type=error_info.error_type,
            message=error_info.message,
            context=error_info.context,
            stack_trace=error_info.stack_trace
        )
        
        # Call error callbacks
        for callback in self.error_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(error_info)
                else:
                    callback(error_info)
            except Exception as callback_error:
                logger.error("Error callback failed", error=str(callback_error))
        
        return error_info
    
    def _update_metrics(self, error_info: ErrorInfo):
        """Update error metrics"""
        # Update total count
        self.error_metrics.total_errors += 1
        
        # Update by type
        error_type = error_info.error_type
        self.error_metrics.errors_by_type[error_type] = (
            self.error_metrics.errors_by_type.get(error_type, 0) + 1
        )
        
        # Update by endpoint if available
        endpoint = error_info.context.get("endpoint")
        if endpoint:
            self.error_metrics.errors_by_endpoint[endpoint] = (
                self.error_metrics.errors_by_endpoint.get(endpoint, 0) + 1
            )
        
        # Add to recent errors
        self.error_metrics.recent_errors.append(error_info)
        
        # Keep only recent errors
        if len(self.error_metrics.recent_errors) > self.max_recent_errors:
            self.error_metrics.recent_errors = self.error_metrics.recent_errors[-self.max_recent_errors:]
        
        # Calculate error rate
        self._calculate_error_rate()
    
    def _calculate_error_rate(self):
        """Calculate error rate per hour"""
        if not self.error_metrics.recent_errors:
            self.error_metrics.error_rate_per_hour = 0.0
            return
        
        # Count errors in the last hour
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        recent_errors_count = 0
        
        for error in self.error_metrics.recent_errors:
            error_time = datetime.fromisoformat(error.timestamp.replace("Z", "+00:00"))
            if error_time > one_hour_ago:
                recent_errors_count += 1
        
        self.error_metrics.error_rate_per_hour = recent_errors_count
    
    def get_metrics(self) -> ErrorMetrics:
        """Get current error metrics"""
        self._calculate_error_rate()
        return self.error_metrics
    
    def reset_metrics(self):
        """Reset error metrics"""
        self.error_metrics = ErrorMetrics()
        logger.info("Error metrics reset")


# Global error handler instance
error_handler = ErrorHandler()


def handle_exceptions(user_message: str = None):
    """Decorator for handling exceptions in async functions"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except HTTPException:
                # Re-raise HTTP exceptions
                raise
            except Exception as e:
                # Handle other exceptions
                context = {
                    "function": func.__name__,
                    "args": str(args)[:200],
                    "kwargs": str(kwargs)[:200]
                }
                
                error_info = await error_handler.handle_error(
                    e, 
                    context=context, 
                    user_message=user_message
                )
                
                # Convert to HTTP exception
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error_id": error_info.error_id,
                        "message": user_message or "An internal error occurred",
                        "type": error_info.error_type
                    }
                )
        return wrapper
    return decorator


def handle_sync_exceptions(user_message: str = None):
    """Decorator for handling exceptions in sync functions"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Handle exceptions synchronously
                context = {
                    "function": func.__name__,
                    "args": str(args)[:200],
                    "kwargs": str(kwargs)[:200]
                }
                
                import uuid
                error_id = str(uuid.uuid4())
                
                logger.error(
                    "Sync function error",
                    error_id=error_id,
                    error_type=type(e).__name__,
                    message=str(e),
                    context=context
                )
                
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error_id": error_id,
                        "message": user_message or "An internal error occurred",
                        "type": type(e).__name__
                    }
                )
        return wrapper
    return decorator


async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Global exception handler for FastAPI"""
    context = {
        "method": request.method,
        "url": str(request.url),
        "headers": dict(request.headers),
        "endpoint": request.url.path
    }
    
    error_info = await error_handler.handle_error(
        exc,
        context=context,
        user_message="An unexpected error occurred"
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error_id": error_info.error_id,
            "message": "An unexpected error occurred",
            "type": error_info.error_type,
            "timestamp": error_info.timestamp
        }
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handler for HTTP exceptions"""
    context = {
        "method": request.method,
        "url": str(request.url),
        "status_code": exc.status_code,
        "endpoint": request.url.path
    }
    
    # Log HTTP exceptions for monitoring
    logger.warning(
        "HTTP exception",
        status_code=exc.status_code,
        detail=exc.detail,
        context=context
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.utcnow().isoformat()
        }
    )


class HealthChecker:
    """System health monitoring"""
    
    def __init__(self):
        self.health_checks: Dict[str, Callable] = {}
        self.last_check_results: Dict[str, Dict[str, Any]] = {}
    
    def register_health_check(self, name: str, check_func: Callable):
        """Register a health check function"""
        self.health_checks[name] = check_func
    
    async def run_health_checks(self) -> Dict[str, Any]:
        """Run all health checks"""
        results = {}
        overall_healthy = True
        
        for name, check_func in self.health_checks.items():
            try:
                if asyncio.iscoroutinefunction(check_func):
                    result = await check_func()
                else:
                    result = check_func()
                
                results[name] = {
                    "status": "healthy" if result else "unhealthy",
                    "details": result if isinstance(result, dict) else {"result": result},
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                if not result:
                    overall_healthy = False
                    
            except Exception as e:
                results[name] = {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
                overall_healthy = False
        
        self.last_check_results = results
        
        return {
            "overall_status": "healthy" if overall_healthy else "unhealthy",
            "checks": results,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def get_last_results(self) -> Dict[str, Any]:
        """Get last health check results"""
        return self.last_check_results


# Global health checker instance
health_checker = HealthChecker()


async def setup_error_monitoring():
    """Setup error monitoring and alerting"""
    
    async def critical_error_callback(error_info: ErrorInfo):
        """Handle critical errors"""
        critical_error_types = [
            "DatabaseError",
            "RedisConnectionError", 
            "ExternalServiceError",
            "SecurityError"
        ]
        
        if error_info.error_type in critical_error_types:
            logger.critical(
                "Critical error detected",
                error_id=error_info.error_id,
                error_type=error_info.error_type,
                message=error_info.message
            )
            
            # Here you could add alerting logic:
            # - Send email/SMS alerts
            # - Post to Slack/Discord
            # - Create incident tickets
            # - Trigger automated recovery
    
    error_handler.add_error_callback(critical_error_callback)
    
    # Register basic health checks
    health_checker.register_health_check("error_rate", lambda: error_handler.get_metrics().error_rate_per_hour < 10)
    health_checker.register_health_check("recent_errors", lambda: len(error_handler.get_metrics().recent_errors) < 50)
    
    logger.info("Error monitoring setup complete")
