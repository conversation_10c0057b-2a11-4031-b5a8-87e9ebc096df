@echo off

echo ================================
echo   AP3X Debug Startup Script
echo ================================
echo.

:: Show current directory
echo Current directory: %CD%
echo.

:: Check if directories exist
echo Checking directories...
if exist "backend" (
    echo [OK] backend directory found
) else (
    echo [ERROR] backend directory NOT found
)

if exist "UI" (
    echo [OK] UI directory found
) else (
    echo [ERROR] UI directory NOT found
)

if exist "backend\api\main.py" (
    echo [OK] backend\api\main.py found
) else (
    echo [ERROR] backend\api\main.py NOT found
)

if exist "UI\package.json" (
    echo [OK] UI\package.json found
) else (
    echo [ERROR] UI\package.json NOT found
)

echo.

:: Check Python
echo Checking Python...
python --version 2>nul
if %errorlevel% equ 0 (
    echo [OK] Python is available
) else (
    echo [ERROR] Python is NOT available
)

:: Check Node/NPM
echo Checking Node.js...
node --version 2>nul
if %errorlevel% equ 0 (
    echo [OK] Node.js is available
) else (
    echo [ERROR] Node.js is NOT available
)

npm --version 2>nul
if %errorlevel% equ 0 (
    echo [OK] NPM is available
) else (
    echo [ERROR] NPM is NOT available
)

echo.

:: Check ports
echo Checking ports...
netstat -an | find ":8000 " >nul
if %errorlevel% equ 0 (
    echo [BUSY] Port 8000 is in use
) else (
    echo [FREE] Port 8000 is available
)

netstat -an | find ":3000 " >nul
if %errorlevel% equ 0 (
    echo [BUSY] Port 3000 is in use
) else (
    echo [FREE] Port 3000 is available
)

echo.

:: Test backend directory
echo Testing backend...
cd backend 2>nul
if %errorlevel% equ 0 (
    echo [OK] Can access backend directory
    echo Current directory: %CD%
    
    :: Check if uvicorn is available
    python -m uvicorn --help >nul 2>&1
    if %errorlevel% equ 0 (
        echo [OK] uvicorn is available
    ) else (
        echo [ERROR] uvicorn is NOT available
        echo Try: pip install uvicorn
    )
    
    cd ..
) else (
    echo [ERROR] Cannot access backend directory
)

echo.

:: Test UI directory
echo Testing UI...
cd UI 2>nul
if %errorlevel% equ 0 (
    echo [OK] Can access UI directory
    echo Current directory: %CD%
    
    :: Check if node_modules exists
    if exist "node_modules" (
        echo [OK] node_modules found
    ) else (
        echo [WARNING] node_modules NOT found
        echo Try: npm install
    )
    
    cd ..
) else (
    echo [ERROR] Cannot access UI directory
)

echo.
echo ================================
echo Debug complete!
echo ================================
echo.

:: Ask if user wants to try starting anyway
set /p choice="Do you want to try starting the services anyway? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo Starting services...
    
    :: Simple start without port detection
    echo Starting backend on port 8000...
    cd backend
    start "Backend Debug" cmd /k "echo Backend starting... && python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload"
    
    timeout /t 3 >nul
    
    echo Starting frontend on port 3000...
    cd ..\UI
    start "Frontend Debug" cmd /k "echo Frontend starting... && npm run dev"
    
    echo.
    echo Services started in debug mode.
    echo Check the opened windows for any error messages.
)

echo.
pause
