# AP3X Debug Startup Script (PowerShell)
# Checks system requirements and diagnoses issues

Write-Host "================================" -ForegroundColor Cyan
Write-Host "   AP3X Debug Startup Script" -ForegroundColor Cyan  
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

# Show current directory
Write-Host "Current directory: $PWD" -ForegroundColor Yellow
Write-Host ""

# Check if directories exist
Write-Host "Checking directories..." -ForegroundColor Blue

if (Test-Path "backend") {
    Write-Host "[OK] backend directory found" -ForegroundColor Green
} else {
    Write-Host "[ERROR] backend directory NOT found" -ForegroundColor Red
}

if (Test-Path "UI") {
    Write-Host "[OK] UI directory found" -ForegroundColor Green
} else {
    Write-Host "[ERROR] UI directory NOT found" -ForegroundColor Red
}

if (Test-Path "backend\api\main.py") {
    Write-Host "[OK] backend\api\main.py found" -ForegroundColor Green
} else {
    Write-Host "[ERROR] backend\api\main.py NOT found" -ForegroundColor Red
}

if (Test-Path "UI\package.json") {
    Write-Host "[OK] UI\package.json found" -ForegroundColor Green
} else {
    Write-Host "[ERROR] UI\package.json NOT found" -ForegroundColor Red
}

Write-Host ""

# Check Python
Write-Host "Checking Python..." -ForegroundColor Blue
try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion) {
        Write-Host "[OK] Python is available: $pythonVersion" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Python is NOT available" -ForegroundColor Red
    }
} catch {
    Write-Host "[ERROR] Python is NOT available" -ForegroundColor Red
}

# Check Node.js
Write-Host "Checking Node.js..." -ForegroundColor Blue
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "[OK] Node.js is available: $nodeVersion" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Node.js is NOT available" -ForegroundColor Red
    }
} catch {
    Write-Host "[ERROR] Node.js is NOT available" -ForegroundColor Red
}

try {
    $npmVersion = npm --version 2>$null
    if ($npmVersion) {
        Write-Host "[OK] NPM is available: $npmVersion" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] NPM is NOT available" -ForegroundColor Red
    }
} catch {
    Write-Host "[ERROR] NPM is NOT available" -ForegroundColor Red
}

Write-Host ""

# Check ports
Write-Host "Checking ports..." -ForegroundColor Blue

$port8000 = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue
if ($port8000) {
    Write-Host "[BUSY] Port 8000 is in use" -ForegroundColor Yellow
} else {
    Write-Host "[FREE] Port 8000 is available" -ForegroundColor Green
}

$port3000 = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
if ($port3000) {
    Write-Host "[BUSY] Port 3000 is in use" -ForegroundColor Yellow
} else {
    Write-Host "[FREE] Port 3000 is available" -ForegroundColor Green
}

Write-Host ""

# Test backend directory
Write-Host "Testing backend..." -ForegroundColor Blue
if (Test-Path "backend") {
    Push-Location "backend"
    Write-Host "[OK] Can access backend directory" -ForegroundColor Green
    Write-Host "Current directory: $PWD" -ForegroundColor Gray
    
    # Check if uvicorn is available
    try {
        $uvicornHelp = python -m uvicorn --help 2>$null
        if ($uvicornHelp) {
            Write-Host "[OK] uvicorn is available" -ForegroundColor Green
        } else {
            Write-Host "[ERROR] uvicorn is NOT available" -ForegroundColor Red
            Write-Host "Try: pip install uvicorn" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[ERROR] uvicorn is NOT available" -ForegroundColor Red
        Write-Host "Try: pip install uvicorn" -ForegroundColor Yellow
    }
    
    Pop-Location
} else {
    Write-Host "[ERROR] Cannot access backend directory" -ForegroundColor Red
}

Write-Host ""

# Test UI directory
Write-Host "Testing UI..." -ForegroundColor Blue
if (Test-Path "UI") {
    Push-Location "UI"
    Write-Host "[OK] Can access UI directory" -ForegroundColor Green
    Write-Host "Current directory: $PWD" -ForegroundColor Gray
    
    # Check if node_modules exists
    if (Test-Path "node_modules") {
        Write-Host "[OK] node_modules found" -ForegroundColor Green
    } else {
        Write-Host "[WARNING] node_modules NOT found" -ForegroundColor Yellow
        Write-Host "Try: npm install" -ForegroundColor Yellow
    }
    
    Pop-Location
} else {
    Write-Host "[ERROR] Cannot access UI directory" -ForegroundColor Red
}

Write-Host ""
Write-Host "================================" -ForegroundColor Cyan
Write-Host "Debug complete!" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

# Ask if user wants to try starting anyway
$choice = Read-Host "Do you want to try starting the services anyway? (y/n)"
if ($choice -eq "y" -or $choice -eq "Y") {
    Write-Host ""
    Write-Host "Starting services..." -ForegroundColor Blue
    
    # Simple start without port detection
    Write-Host "Starting backend on port 8000..." -ForegroundColor Blue
    Push-Location "backend"
    Start-Process cmd -ArgumentList "/k", "echo Backend starting... && python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload" -WindowStyle Normal
    Pop-Location
    
    Start-Sleep -Seconds 3
    
    Write-Host "Starting frontend on port 3000..." -ForegroundColor Blue
    Push-Location "UI"
    Start-Process cmd -ArgumentList "/k", "echo Frontend starting... && npm run dev" -WindowStyle Normal
    Pop-Location
    
    Write-Host ""
    Write-Host "Services started in debug mode." -ForegroundColor Green
    Write-Host "Check the opened windows for any error messages." -ForegroundColor Yellow
    
    Start-Sleep -Seconds 8
    Write-Host "Opening browser..." -ForegroundColor Blue
    Start-Process "http://localhost:3000"
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
