"use client"

import { useState, useEffect, useCallback } from "react"

interface Agent {
  id: string
  name: string
  type: string
  status: "active" | "idle" | "busy"
}

interface AP3XResponse {
  success: boolean
  data: {
    sessionId: string
    changes: Array<{
      file: string
      description: string
      type: "create" | "modify" | "delete"
    }>
  }
}

interface ChatMessage {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
}

interface ChatMessage {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
  isStreaming?: boolean
  error?: string
}

interface BackendStatus {
  status: string
  version: string
  agent_status: string
  external_services: Record<string, string>
}

export function useAP3X() {
  const [isBackendAvailable, setIsBackendAvailable] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [agents, setAgents] = useState<Agent[]>([])
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [sessionId, setSessionId] = useState<string>("")
  const [backendStatus, setBackendStatus] = useState<BackendStatus | null>(null)

  // WebSocket disabled - using REST API only
  const isConnected = false

  const checkBackendConnection = useCallback(async () => {
    try {
      const response = await fetch("http://localhost:8000/health", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (response.ok) {
        const status = await response.json()
        setBackendStatus(status)
        setIsBackendAvailable(true)

        // Skip WebSocket for now - use REST API only
        // connectWebSocket()
        console.log("Backend connected via REST API")
      } else {
        setIsBackendAvailable(false)
        setBackendStatus(null)
      }
    } catch (error) {
      console.error("Backend connection failed:", error)
      setIsBackendAvailable(false)
      setBackendStatus(null)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // WebSocket functionality removed - using REST API only

  const sendMessage = useCallback(async (message: string): Promise<void> => {
    if (!isBackendAvailable) {
      throw new Error("AP3X Backend is not available")
    }

    // Add user message to chat
    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      type: "user", 
      content: message,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, userMessage])

    try {
      // Use REST API for now (WebSocket disabled)
      console.log("Sending message via REST API:", message)
      const response = await fetch("http://localhost:8000/query", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: message,
          session_id: sessionId,
          stream: false
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      console.log("Backend response:", result)

      // Handle different response formats
      let content = "Response received"
      if (result.final_answer) {
        content = result.final_answer
      } else if (result.response) {
        content = result.response
      } else if (result.message) {
        content = result.message
      } else if (typeof result === 'string') {
        content = result
      } else if (result.error) {
        content = `Error: ${result.error}`
      }

      // Add AI response
      const aiMessage: ChatMessage = {
        id: `ai_${Date.now()}`,
        type: "ai",
        content: content,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, aiMessage])

    } catch (error) {
      console.error("Error sending message:", error)
      // Add error message
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        type: "ai",
        content: "Sorry, I encountered an error processing your request.",
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    }
  }, [isBackendAvailable, sessionId])

  const editCode = useCallback(
    async (params: {
      prompt: string
      useMultiAgent?: boolean
    }): Promise<AP3XResponse> => {
      if (!isBackendAvailable) {
        throw new Error("AP3X Backend is not available")
      }

      const response = await fetch("http://localhost:8000/query", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: params.prompt,
          session_id: sessionId,
          stream: false
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      // Transform the response to match expected format
      return {
        success: true,
        data: {
          sessionId: result.session_id || sessionId,
          changes: [] // This would be populated based on actual agent response
        }
      }
    },
    [isBackendAvailable, sessionId],
  )

  const retryConnection = useCallback(() => {
    setIsLoading(true)
    checkBackendConnection()
  }, [checkBackendConnection])

  useEffect(() => {
    checkBackendConnection()
  }, [checkBackendConnection])

  return {
    isBackendAvailable,
    isConnected,
    isLoading,
    agents,
    messages,
    sessionId,
    sendMessage,
    editCode,
    retryConnection,
    backendStatus,
    clearMessages: () => setMessages([]),
    connectionStatus: isBackendAvailable ? 'rest_api_connected' : 'disconnected'
  }
}
