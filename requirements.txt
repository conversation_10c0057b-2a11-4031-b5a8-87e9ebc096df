# Core LangGraph and LangChain dependencies
langgraph>=0.2.0
langchain-openai>=0.2.0
langchain-core>=0.3.0

# Official MCP Python SDK
mcp>=1.0.0

# LangChain MCP Adapters for integration
langchain-mcp-adapters>=0.1.0

# FastAPI and WebSocket support
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0

# HTTP client and data validation
httpx>=0.27.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Environment and configuration
python-dotenv>=1.0.0
python-multipart>=0.0.6

# Logging and monitoring
structlog>=23.0.0
rich>=13.0.0

# Session and state management
redis>=5.0.0

# Social media integration
tweepy>=4.14.0

# Additional utilities
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Testing dependencies (optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0