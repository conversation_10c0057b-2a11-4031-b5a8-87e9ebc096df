#!/usr/bin/env python3
"""
Setup script for AP3X Crypto Agent Backend
Automates installation and configuration
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, check=True, shell=False):
    """Run a command and handle errors"""
    print(f"Running: {command}")
    try:
        if shell:
            result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        else:
            result = subprocess.run(command.split(), check=check, capture_output=True, text=True)
        
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_python_version():
    """Check Python version"""
    print("Checking Python version...")
    if sys.version_info < (3, 9):
        print("Error: Python 3.9 or higher is required")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")


def check_node_version():
    """Check Node.js version"""
    print("Checking Node.js version...")
    try:
        result = run_command("node --version")
        version = result.stdout.strip()
        print(f"✓ Node.js {version} detected")
        return True
    except:
        print("⚠ Node.js not found. Some MCP servers may not work.")
        return False


def check_redis():
    """Check if Redis is available"""
    print("Checking Redis...")
    try:
        result = run_command("redis-cli ping", check=False)
        if "PONG" in result.stdout:
            print("✓ Redis is running")
            return True
        else:
            print("⚠ Redis is not running")
            return False
    except:
        print("⚠ Redis not found")
        return False


def install_python_dependencies():
    """Install Python dependencies"""
    print("Installing Python dependencies...")

    # Upgrade pip
    run_command(f"{sys.executable} -m pip install --upgrade pip")

    # Install requirements
    if Path("requirements.txt").exists():
        run_command(f"{sys.executable} -m pip install -r requirements.txt")
    else:
        print("Error: requirements.txt not found")
        sys.exit(1)

    # Install MCP Python SDK if not in requirements
    try:
        run_command(f"{sys.executable} -m pip install mcp")
        print("✓ MCP Python SDK installed")
    except:
        print("⚠ Failed to install MCP Python SDK")

    # Try to install langchain-mcp-adapters
    try:
        run_command(f"{sys.executable} -m pip install langchain-mcp-adapters")
        print("✓ LangChain MCP adapters installed")
    except:
        print("⚠ LangChain MCP adapters not available (may be in development)")

    print("✓ Python dependencies installed")


def install_mcp_servers():
    """Install MCP servers"""
    print("Installing MCP servers...")
    
    if not check_node_version():
        print("Skipping MCP server installation (Node.js not available)")
        return
    
    # Install Sequential Thinking MCP
    try:
        run_command("npm install -g @modelcontextprotocol/server-sequential-thinking")
        print("✓ Sequential Thinking MCP server installed")
    except:
        print("⚠ Failed to install Sequential Thinking MCP server")
    
    # Install Moralis MCP
    try:
        run_command("npm install -g @moralisweb3/api-mcp-server")
        print("✓ Moralis MCP server installed")
    except:
        print("⚠ Failed to install Moralis MCP server")
    
    # Install Fetch MCP (using uvx)
    try:
        run_command("pip install mcp-server-fetch")
        print("✓ Fetch MCP server installed")
    except:
        print("⚠ Failed to install Fetch MCP server")


def create_env_file():
    """Create .env file from template"""
    print("Creating environment configuration...")
    
    env_template = """# AP3X Crypto Agent Backend Configuration

# Application Settings
SECRET_KEY=your-secret-key-change-this-in-production
DEBUG=true
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=development

# OpenRouter/LLM Configuration (REQUIRED)
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=moonshotai/kimi-k2

# Moralis Configuration (REQUIRED)
MORALIS_API_KEY=your_moralis_api_key_here

# Web Research Configuration (Optional)
TAVILY_API_KEY=your_tavily_api_key_here
BRAVE_API_KEY=your_brave_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_google_cse_id_here

# Twitter Integration (Optional)
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here
TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret_here

# Phantom Wallet Integration (Optional)
PHANTOM_APP_ID=your_phantom_app_id_here
PHANTOM_REDIRECT_URL=http://localhost:8000/phantom/callback

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Session Configuration
SESSION_TIMEOUT_MINUTES=60
MAX_SESSIONS_PER_USER=10

# Agent Configuration
AGENT_MAX_THINKING_STEPS=10
AGENT_CONFIDENCE_THRESHOLD=0.7
AGENT_TIMEOUT_SECONDS=300

# Blockchain Configuration
DEFAULT_CHAIN=eth
SUPPORTED_CHAINS=eth,polygon,bsc,arbitrum,optimism,base

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# External Service Timeouts
MORALIS_TIMEOUT_SECONDS=30
WEB_RESEARCH_TIMEOUT_SECONDS=60
TWITTER_TIMEOUT_SECONDS=30
"""
    
    if not Path(".env").exists():
        with open(".env", "w") as f:
            f.write(env_template)
        print("✓ .env file created")
        print("⚠ Please edit .env file with your API keys before running the application")
    else:
        print("✓ .env file already exists")


def create_startup_scripts():
    """Create startup scripts"""
    print("Creating startup scripts...")
    
    # Create start_mcp_servers.sh
    mcp_script = """#!/bin/bash

echo "Starting MCP servers for AP3X Crypto Agent..."

# Start Sequential Thinking MCP server
echo "Starting Sequential Thinking MCP server on port 3000..."
npx -y @modelcontextprotocol/server-sequential-thinking &
SEQUENTIAL_PID=$!

# Start Moralis MCP server
echo "Starting Moralis MCP server on port 3001..."
MORALIS_API_KEY=${MORALIS_API_KEY} npx @moralisweb3/api-mcp-server --port 3001 &
MORALIS_PID=$!

# Start Fetch MCP server
echo "Starting Fetch MCP server on port 3002..."
uvx mcp-server-fetch --port 3002 &
FETCH_PID=$!

echo "MCP servers started:"
echo "  Sequential Thinking: PID $SEQUENTIAL_PID (port 3000)"
echo "  Moralis: PID $MORALIS_PID (port 3001)"
echo "  Fetch: PID $FETCH_PID (port 3002)"

echo "To stop all MCP servers, run: kill $SEQUENTIAL_PID $MORALIS_PID $FETCH_PID"

# Wait for all background processes
wait
"""
    
    with open("start_mcp_servers.sh", "w") as f:
        f.write(mcp_script)
    
    # Make executable
    os.chmod("start_mcp_servers.sh", 0o755)
    
    # Create start_agent.sh
    agent_script = """#!/bin/bash

echo "Starting AP3X Crypto Agent Backend..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Error: .env file not found. Please run setup.py first."
    exit 1
fi

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "Warning: Redis is not running. Starting Redis..."
    redis-server --daemonize yes
fi

# Start the agent
echo "Starting FastAPI server..."
python main.py
"""
    
    with open("start_agent.sh", "w") as f:
        f.write(agent_script)
    
    # Make executable
    os.chmod("start_agent.sh", 0o755)
    
    print("✓ Startup scripts created")


def run_tests():
    """Run tests to verify installation"""
    print("Running tests to verify installation...")
    
    try:
        # Install test dependencies
        run_command(f"{sys.executable} -m pip install pytest pytest-asyncio httpx")
        
        # Run tests
        result = run_command(f"{sys.executable} -m pytest test_main.py::TestHealthEndpoint -v", check=False)
        
        if result.returncode == 0:
            print("✓ Basic tests passed")
        else:
            print("⚠ Some tests failed, but installation should still work")
    
    except:
        print("⚠ Could not run tests, but installation should still work")


def main():
    """Main setup function"""
    print("=" * 60)
    print("AP3X Crypto Agent Backend Setup")
    print("=" * 60)
    
    # Check prerequisites
    check_python_version()
    has_node = check_node_version()
    has_redis = check_redis()
    
    # Install dependencies
    install_python_dependencies()
    
    if has_node:
        install_mcp_servers()
    
    # Create configuration
    create_env_file()
    create_startup_scripts()
    
    # Run tests
    run_tests()
    
    print("\n" + "=" * 60)
    print("Setup Complete!")
    print("=" * 60)
    
    print("\nNext steps:")
    print("1. Edit .env file with your API keys")
    
    if not has_redis:
        print("2. Install and start Redis server")
    
    print("3. Start MCP servers: ./start_mcp_servers.sh")
    print("4. Start the agent: ./start_agent.sh")
    print("5. Visit http://localhost:8000/health to verify")
    
    print("\nFor more information, see README.md")


if __name__ == "__main__":
    main()
