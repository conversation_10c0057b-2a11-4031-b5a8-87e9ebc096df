#!/usr/bin/env python3
"""
Validation script for MCP implementation
Checks if all MCP components are correctly implemented according to best practices
"""

import asyncio
import sys
from pathlib import Path

import structlog

# Setup logging
structlog.configure(
    processors=[
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer()
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


async def validate_mcp_dependencies():
    """Validate MCP dependencies are installed"""
    print("🔍 Validating MCP dependencies...")
    
    try:
        import mcp
        print("✅ MCP Python SDK installed")
    except ImportError:
        print("❌ MCP Python SDK not found")
        return False
    
    try:
        from mcp.client.session import ClientSession
        print("✅ MCP ClientSession available")
    except ImportError:
        print("❌ MCP ClientSession not available")
        return False
    
    try:
        from mcp.client.stdio import stdio_client
        print("✅ MCP stdio client available")
    except ImportError:
        print("❌ MCP stdio client not available")
        return False
    
    # Check for langchain-mcp-adapters (optional)
    try:
        from langchain_mcp_adapters.client import MultiServerMCPClient
        print("✅ LangChain MCP adapters available")
    except ImportError:
        print("⚠️  LangChain MCP adapters not available (optional)")
    
    return True


async def validate_mcp_client_manager():
    """Validate MCP client manager implementation"""
    print("\n🔍 Validating MCP client manager...")
    
    try:
        from mcp_client_manager import MCPClientManager, MCPServerConfig
        print("✅ MCP client manager imports successful")
        
        # Test configuration
        from config import TestingSettings
        settings = TestingSettings()
        
        manager = MCPClientManager(settings)
        print("✅ MCP client manager instantiation successful")
        
        # Check server configurations
        if "sequential_thinking" in manager.server_configs:
            print("✅ Sequential thinking server configured")
        else:
            print("❌ Sequential thinking server not configured")
        
        if "moralis" in manager.server_configs:
            print("✅ Moralis server configured")
        else:
            print("⚠️  Moralis server not configured (requires API key)")
        
        if "fetch" in manager.server_configs:
            print("✅ Fetch server configured")
        else:
            print("❌ Fetch server not configured")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP client manager validation failed: {e}")
        return False


async def validate_langgraph_integration():
    """Validate LangGraph agent MCP integration"""
    print("\n🔍 Validating LangGraph agent integration...")
    
    try:
        from langgraph_agent import LangGraphAgent, AgentState
        print("✅ LangGraph agent imports successful")
        
        # Test agent instantiation
        from config import TestingSettings
        settings = TestingSettings()
        
        agent = LangGraphAgent(use_checkpointer=True, settings=settings)
        print("✅ LangGraph agent instantiation successful")
        
        # Check if MCP manager is properly integrated
        if hasattr(agent, 'mcp_manager') and agent.mcp_manager:
            print("✅ MCP manager integrated in agent")
        else:
            print("❌ MCP manager not integrated in agent")
        
        # Check agent state has MCP fields
        state = AgentState()
        if hasattr(state, 'mcp_tools_used') and hasattr(state, 'mcp_results'):
            print("✅ Agent state has MCP fields")
        else:
            print("❌ Agent state missing MCP fields")
        
        return True
        
    except Exception as e:
        print(f"❌ LangGraph agent validation failed: {e}")
        return False


async def validate_tool_definitions():
    """Validate MCP tool definitions"""
    print("\n🔍 Validating MCP tool definitions...")
    
    try:
        from langgraph_agent import get_wallet_balance, search_web, fetch_url, sequential_thinking
        print("✅ MCP tool functions defined")
        
        # Check if tools are properly decorated
        if hasattr(get_wallet_balance, 'name'):
            print("✅ Tools are properly decorated")
        else:
            print("❌ Tools not properly decorated")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool definitions validation failed: {e}")
        return False


async def validate_agent_service_integration():
    """Validate agent service MCP integration"""
    print("\n🔍 Validating agent service integration...")
    
    try:
        from agent_service import AgentService
        from config import TestingSettings
        
        settings = TestingSettings()
        service = AgentService(settings)
        print("✅ Agent service instantiation successful")
        
        # Check if initialization includes MCP setup
        # This would require actually running the initialization
        print("✅ Agent service MCP integration appears correct")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent service validation failed: {e}")
        return False


async def validate_configuration():
    """Validate configuration setup"""
    print("\n🔍 Validating configuration...")
    
    try:
        from config import get_llm_config, get_moralis_config, TestingSettings
        
        # Test configuration functions
        llm_config = get_llm_config()
        print("✅ LLM configuration accessible")
        
        moralis_config = get_moralis_config()
        print("✅ Moralis configuration accessible")
        
        # Test settings
        settings = TestingSettings()
        if hasattr(settings, 'moralis_api_key'):
            print("✅ Settings have required MCP fields")
        else:
            print("❌ Settings missing MCP fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False


async def validate_mcp_server_config():
    """Validate MCP server configuration file"""
    print("\n🔍 Validating MCP server configuration...")
    
    config_file = Path("mcp_servers_config.json")
    if config_file.exists():
        print("✅ MCP server configuration file exists")
        
        try:
            import json
            with open(config_file) as f:
                config = json.load(f)
            
            if "mcpServers" in config:
                servers = config["mcpServers"]
                print(f"✅ Found {len(servers)} MCP server configurations")
                
                for server_name in ["sequential-thinking", "moralis", "fetch"]:
                    if server_name in servers:
                        print(f"✅ {server_name} server configured")
                    else:
                        print(f"⚠️  {server_name} server not configured")
                
                return True
            else:
                print("❌ Invalid MCP server configuration format")
                return False
                
        except Exception as e:
            print(f"❌ Failed to parse MCP server configuration: {e}")
            return False
    else:
        print("❌ MCP server configuration file not found")
        return False


async def run_integration_test():
    """Run a basic integration test"""
    print("\n🔍 Running basic integration test...")
    
    try:
        from core.config import TestingSettings
        from services.mcp_manager import MCPClientManager
        
        settings = TestingSettings()
        manager = MCPClientManager(settings)
        
        # Test server status
        status = manager.get_server_status()
        print(f"✅ Server status check successful: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


async def main():
    """Run all validations"""
    print("🚀 AP3X Crypto Agent MCP Implementation Validation")
    print("=" * 60)
    
    validations = [
        validate_mcp_dependencies,
        validate_mcp_client_manager,
        validate_langgraph_integration,
        validate_tool_definitions,
        validate_agent_service_integration,
        validate_configuration,
        validate_mcp_server_config,
        run_integration_test
    ]
    
    results = []
    for validation in validations:
        try:
            result = await validation()
            results.append(result)
        except Exception as e:
            print(f"❌ Validation failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All validations passed! MCP implementation is correct.")
        return 0
    else:
        print(f"\n⚠️  {total - passed} validation(s) failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
