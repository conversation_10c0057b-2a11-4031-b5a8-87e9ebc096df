"""
Session Manager for AP3X Crypto Agent Backend
Handles session state persistence and management
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

import redis.asyncio as redis
import structlog
from pydantic import BaseModel

from core.config import Settings

logger = structlog.get_logger()


class SessionData(BaseModel):
    """Session data model"""
    session_id: str
    created_at: datetime
    last_activity: datetime
    message_count: int = 0
    status: str = "active"
    metadata: Dict[str, Any] = {}
    user_id: Optional[str] = None


class QueryRecord(BaseModel):
    """Query record model"""
    query: str
    response: Dict[str, Any]
    timestamp: datetime
    processing_time: float
    status: str


class SessionManager:
    """Manages user sessions and conversation state"""
    
    def __init__(self, redis: redis.Redis, settings: Settings):
        self.redis = redis
        self.settings = settings
        self.session_prefix = "session:"
        self.history_prefix = "history:"
        self.metrics_key = "agent:metrics"
    
    async def create_session(self, session_id: str, user_id: Optional[str] = None) -> SessionData:
        """Create a new session"""
        now = datetime.utcnow()
        
        session_data = SessionData(
            session_id=session_id,
            created_at=now,
            last_activity=now,
            user_id=user_id
        )
        
        # Store session data
        await self.redis.setex(
            f"{self.session_prefix}{session_id}",
            timedelta(minutes=self.settings.session_timeout_minutes),
            session_data.json()
        )
        
        logger.info("Session created", session_id=session_id, user_id=user_id)
        return session_data
    
    async def get_session(self, session_id: str) -> Optional[SessionData]:
        """Get session data"""
        session_json = await self.redis.get(f"{self.session_prefix}{session_id}")
        if not session_json:
            return None
        
        try:
            return SessionData.parse_raw(session_json)
        except Exception as e:
            logger.error("Failed to parse session data", session_id=session_id, error=str(e))
            return None
    
    async def update_session_activity(self, session_id: str) -> bool:
        """Update session last activity timestamp"""
        session = await self.get_session(session_id)
        if not session:
            # Create new session if it doesn't exist
            session = await self.create_session(session_id)
        
        session.last_activity = datetime.utcnow()
        session.message_count += 1
        
        # Update session with new expiration
        await self.redis.setex(
            f"{self.session_prefix}{session_id}",
            timedelta(minutes=self.settings.session_timeout_minutes),
            session.json()
        )
        
        return True
    
    async def delete_session(self, session_id: str):
        """Delete a session and its history"""
        # Delete session data
        await self.redis.delete(f"{self.session_prefix}{session_id}")
        
        # Delete session history
        await self.redis.delete(f"{self.history_prefix}{session_id}")
        
        logger.info("Session deleted", session_id=session_id)
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        session = await self.get_session(session_id)
        if not session:
            return None
        
        return {
            "session_id": session.session_id,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "message_count": session.message_count,
            "status": session.status,
            "user_id": session.user_id
        }
    
    async def store_query_result(self, session_id: str, query: str, result: Dict[str, Any]):
        """Store query and result in session history"""
        start_time = time.time()
        
        query_record = QueryRecord(
            query=query,
            response=result,
            timestamp=datetime.utcnow(),
            processing_time=result.get("processing_time", 0.0),
            status=result.get("status", "unknown")
        )
        
        # Add to session history (using list)
        history_key = f"{self.history_prefix}{session_id}"
        await self.redis.lpush(history_key, query_record.json())
        
        # Keep only last 100 entries per session
        await self.redis.ltrim(history_key, 0, 99)
        
        # Set expiration on history
        await self.redis.expire(
            history_key,
            timedelta(minutes=self.settings.session_timeout_minutes)
        )
        
        # Update metrics
        await self._update_metrics(query_record)
    
    async def get_session_history(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get session conversation history"""
        history_key = f"{self.history_prefix}{session_id}"
        history_json = await self.redis.lrange(history_key, 0, limit - 1)
        
        history = []
        for record_json in history_json:
            try:
                record = QueryRecord.parse_raw(record_json)
                history.append({
                    "query": record.query,
                    "response": record.response,
                    "timestamp": record.timestamp.isoformat(),
                    "processing_time": record.processing_time,
                    "status": record.status
                })
            except Exception as e:
                logger.error("Failed to parse query record", error=str(e))
                continue
        
        return history
    
    async def list_sessions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """List active sessions"""
        session_keys = await self.redis.keys(f"{self.session_prefix}*")
        sessions = []
        
        for key in session_keys[:limit]:
            session_id = key.replace(self.session_prefix, "")
            session_info = await self.get_session_info(session_id)
            if session_info:
                sessions.append(session_info)
        
        # Sort by last activity (most recent first)
        sessions.sort(key=lambda x: x["last_activity"], reverse=True)
        return sessions
    
    async def cleanup_expired_sessions(self) -> int:
        """Cleanup expired sessions"""
        session_keys = await self.redis.keys(f"{self.session_prefix}*")
        cleaned_count = 0
        
        cutoff_time = datetime.utcnow() - timedelta(minutes=self.settings.session_timeout_minutes)
        
        for key in session_keys:
            session_json = await self.redis.get(key)
            if not session_json:
                continue
            
            try:
                session = SessionData.parse_raw(session_json)
                if session.last_activity < cutoff_time:
                    session_id = session.session_id
                    await self.delete_session(session_id)
                    cleaned_count += 1
            except Exception as e:
                logger.error("Failed to parse session during cleanup", key=key, error=str(e))
                # Delete corrupted session data
                await self.redis.delete(key)
                cleaned_count += 1
        
        return cleaned_count
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get session and query metrics"""
        # Get current session count
        session_keys = await self.redis.keys(f"{self.session_prefix}*")
        active_sessions = len(session_keys)
        
        # Get stored metrics
        metrics_json = await self.redis.get(self.metrics_key)
        if metrics_json:
            try:
                stored_metrics = json.loads(metrics_json)
            except Exception:
                stored_metrics = {}
        else:
            stored_metrics = {}
        
        return {
            "active_sessions": active_sessions,
            "total_queries": stored_metrics.get("total_queries", 0),
            "successful_queries": stored_metrics.get("successful_queries", 0),
            "failed_queries": stored_metrics.get("failed_queries", 0),
            "average_processing_time": stored_metrics.get("average_processing_time", 0.0),
            "last_updated": stored_metrics.get("last_updated"),
        }
    
    async def _update_metrics(self, query_record: QueryRecord):
        """Update global metrics"""
        # Get current metrics
        metrics_json = await self.redis.get(self.metrics_key)
        if metrics_json:
            try:
                metrics = json.loads(metrics_json)
            except Exception:
                metrics = {}
        else:
            metrics = {}
        
        # Update counters
        metrics["total_queries"] = metrics.get("total_queries", 0) + 1
        
        if query_record.status == "success":
            metrics["successful_queries"] = metrics.get("successful_queries", 0) + 1
        else:
            metrics["failed_queries"] = metrics.get("failed_queries", 0) + 1
        
        # Update average processing time
        current_avg = metrics.get("average_processing_time", 0.0)
        total_queries = metrics["total_queries"]
        
        if total_queries > 1:
            metrics["average_processing_time"] = (
                (current_avg * (total_queries - 1) + query_record.processing_time) / total_queries
            )
        else:
            metrics["average_processing_time"] = query_record.processing_time
        
        metrics["last_updated"] = datetime.utcnow().isoformat()
        
        # Store updated metrics
        await self.redis.setex(
            self.metrics_key,
            timedelta(days=7),  # Keep metrics for a week
            json.dumps(metrics)
        )
    
    async def reset_metrics(self):
        """Reset all metrics"""
        await self.redis.delete(self.metrics_key)
        logger.info("Metrics reset")
    
    async def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all sessions for a specific user"""
        session_keys = await self.redis.keys(f"{self.session_prefix}*")
        user_sessions = []
        
        for key in session_keys:
            session_json = await self.redis.get(key)
            if not session_json:
                continue
            
            try:
                session = SessionData.parse_raw(session_json)
                if session.user_id == user_id:
                    user_sessions.append({
                        "session_id": session.session_id,
                        "created_at": session.created_at.isoformat(),
                        "last_activity": session.last_activity.isoformat(),
                        "message_count": session.message_count,
                        "status": session.status
                    })
            except Exception as e:
                logger.error("Failed to parse session", key=key, error=str(e))
                continue
        
        # Sort by last activity (most recent first)
        user_sessions.sort(key=lambda x: x["last_activity"], reverse=True)
        return user_sessions
