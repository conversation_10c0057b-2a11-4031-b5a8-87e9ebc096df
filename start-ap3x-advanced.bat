@echo off
setlocal enabledelayedexpansion

:: AP3X Crypto Agent Advanced Startup Script
:: Dynamically finds ports, updates configs, and provides monitoring

title AP3X Crypto Agent Launcher

echo.
echo ==========================================
echo     AP3X Crypto Agent Advanced Launcher
echo ==========================================
echo.

:: Configuration
set "BACKEND_PORT_START=8000"
set "FRONTEND_PORT_START=3000"
set "MAX_PORT_ATTEMPTS=20"
set "BACKEND_HEALTH_TIMEOUT=30"
set "FRONTEND_READY_TIMEOUT=45"

:: Colors for output (if supported)
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

:: Check if we're in the right directory
if not exist "backend" (
    echo %RED%[ERROR]%RESET% Backend directory not found. Please run from AP3X Crypto Agent root directory.
    pause
    exit /b 1
)

if not exist "UI" (
    echo %RED%[ERROR]%RESET% UI directory not found. Please run from AP3X Crypto Agent root directory.
    pause
    exit /b 1
)

:: Function to check if port is available
:check_port
netstat -an | find ":%1 " >nul 2>&1
exit /b %errorlevel%

:: Function to find available port
:find_port
set "start_port=%1"
set "found_port="
for /l %%i in (%start_port%,1,%start_port%+%MAX_PORT_ATTEMPTS%) do (
    call :check_port %%i
    if !errorlevel! neq 0 (
        set "found_port=%%i"
        goto :port_found
    )
)
:port_found
exit /b 0

:: Find backend port
echo %BLUE%[INFO]%RESET% Scanning for available backend port...
call :find_port %BACKEND_PORT_START%
set "BACKEND_PORT=%found_port%"

if "%BACKEND_PORT%"=="" (
    echo %RED%[ERROR]%RESET% No available backend port found in range %BACKEND_PORT_START%-%BACKEND_PORT_START%+%MAX_PORT_ATTEMPTS%
    pause
    exit /b 1
)
echo %GREEN%[SUCCESS]%RESET% Backend port: %BACKEND_PORT%

:: Find frontend port
echo %BLUE%[INFO]%RESET% Scanning for available frontend port...
call :find_port %FRONTEND_PORT_START%
set "FRONTEND_PORT=%found_port%"

if "%FRONTEND_PORT%"=="" (
    echo %RED%[ERROR]%RESET% No available frontend port found in range %FRONTEND_PORT_START%-%FRONTEND_PORT_START%+%MAX_PORT_ATTEMPTS%
    pause
    exit /b 1
)
echo %GREEN%[SUCCESS]%RESET% Frontend port: %FRONTEND_PORT%

:: Create backup of original hook file
echo %BLUE%[INFO]%RESET% Creating backup of frontend configuration...
if exist "UI\hooks\use-ap3x.ts" (
    copy "UI\hooks\use-ap3x.ts" "UI\hooks\use-ap3x.ts.backup" >nul 2>&1
)

:: Update frontend hook with dynamic backend URL
echo %BLUE%[INFO]%RESET% Updating frontend configuration for backend port %BACKEND_PORT%...
cd /d "%~dp0UI"

powershell -Command "& {
    $content = Get-Content 'hooks\use-ap3x.ts' -Raw
    $content = $content -replace 'http://localhost:\d+', 'http://localhost:%BACKEND_PORT%'
    $content = $content -replace 'localhost:\d+', 'localhost:%BACKEND_PORT%'
    Set-Content 'hooks\use-ap3x.ts' -Value $content
    Write-Host 'Frontend configuration updated successfully'
}"

if %errorlevel% neq 0 (
    echo %RED%[ERROR]%RESET% Failed to update frontend configuration
    pause
    exit /b 1
)

:: Start backend
echo.
echo %BLUE%[INFO]%RESET% Starting backend server...
cd /d "%~dp0backend"
start "AP3X Backend (:%BACKEND_PORT%)" cmd /k "echo Starting AP3X Backend on port %BACKEND_PORT%... && python -m uvicorn api.main:app --host 0.0.0.0 --port %BACKEND_PORT% --reload"

:: Wait and check backend health
echo %BLUE%[INFO]%RESET% Waiting for backend to become ready...
set /a "attempts=0"
:check_backend
set /a "attempts+=1"
if %attempts% gtr %BACKEND_HEALTH_TIMEOUT% (
    echo %RED%[ERROR]%RESET% Backend failed to start within %BACKEND_HEALTH_TIMEOUT% seconds
    goto :cleanup
)

timeout /t 1 /nobreak >nul
curl -s "http://localhost:%BACKEND_PORT%/health" >nul 2>&1
if %errorlevel% neq 0 (
    goto :check_backend
)

echo %GREEN%[SUCCESS]%RESET% Backend is ready at http://localhost:%BACKEND_PORT%

:: Start frontend
echo %BLUE%[INFO]%RESET% Starting frontend server...
cd /d "%~dp0UI"
start "AP3X Frontend (:%FRONTEND_PORT%)" cmd /k "echo Starting AP3X Frontend on port %FRONTEND_PORT%... && npm run dev -- --port %FRONTEND_PORT%"

:: Wait for frontend
echo %BLUE%[INFO]%RESET% Waiting for frontend to become ready...
timeout /t 8 /nobreak >nul

:: Display success information
echo.
echo ==========================================
echo %GREEN%       AP3X Crypto Agent Ready!%RESET%
echo ==========================================
echo.
echo %GREEN%Frontend:%RESET%  http://localhost:%FRONTEND_PORT%
echo %GREEN%Backend:%RESET%   http://localhost:%BACKEND_PORT%
echo.
echo %BLUE%API Documentation:%RESET% http://localhost:%BACKEND_PORT%/docs
echo %BLUE%Health Check:%RESET%      http://localhost:%BACKEND_PORT%/health
echo.
echo %YELLOW%[TIP]%RESET% Both services are running in separate windows
echo %YELLOW%[TIP]%RESET% Close those windows to stop the services
echo.

:: Open browser
echo %BLUE%[INFO]%RESET% Opening AP3X Crypto Agent in browser...
start "" "http://localhost:%FRONTEND_PORT%"

:: Monitoring loop
echo.
echo %BLUE%[INFO]%RESET% Monitoring services... (Press Ctrl+C to exit)
echo.

:monitor_loop
timeout /t 10 /nobreak >nul

:: Check backend health
curl -s "http://localhost:%BACKEND_PORT%/health" >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[WARNING]%RESET% Backend health check failed
) else (
    echo %GREEN%[OK]%RESET% Backend healthy
)

:: Check frontend (basic port check)
call :check_port %FRONTEND_PORT%
if %errorlevel% equ 0 (
    echo %GREEN%[OK]%RESET% Frontend running
) else (
    echo %RED%[WARNING]%RESET% Frontend not responding
)

echo.
goto :monitor_loop

:cleanup
echo.
echo %BLUE%[INFO]%RESET% Cleaning up...
if exist "UI\hooks\use-ap3x.ts.backup" (
    echo %BLUE%[INFO]%RESET% Restoring original frontend configuration...
    copy "UI\hooks\use-ap3x.ts.backup" "UI\hooks\use-ap3x.ts" >nul 2>&1
    del "UI\hooks\use-ap3x.ts.backup" >nul 2>&1
)

pause
exit /b 0
