@echo off

echo.
echo ================================
echo   AP3X Crypto Agent Launcher
echo ================================
echo.

:: Check directories exist
if not exist "backend" (
    echo ERROR: backend directory not found
    echo Please run this from the AP3X Crypto Agent root directory
    pause
    exit /b 1
)

if not exist "UI" (
    echo ERROR: UI directory not found  
    echo Please run this from the AP3X Crypto Agent root directory
    pause
    exit /b 1
)

:: Find available ports
echo Finding available ports...

:: Check backend port
set BACKEND_PORT=8000
:check_backend
netstat -an | find ":8000 " >nul
if %errorlevel% equ 0 (
    set /a BACKEND_PORT+=1
    goto check_backend
)

:: Check frontend port  
set FRONTEND_PORT=3000
:check_frontend
netstat -an | find ":3000 " >nul
if %errorlevel% equ 0 (
    set /a FRONTEND_PORT+=1
    goto check_frontend
)

echo Backend port: %BACKEND_PORT%
echo Frontend port: %FRONTEND_PORT%

:: Start backend
echo.
echo Starting backend...
cd backend
start "Backend" cmd /k "python -m uvicorn api.main:app --host 0.0.0.0 --port %BACKEND_PORT% --reload"

:: Wait for backend
echo Waiting for backend to start...
timeout /t 5 >nul

:: Start frontend
echo Starting frontend...
cd ..\UI
start "Frontend" cmd /k "npm run dev -- --port %FRONTEND_PORT%"

:: Wait for frontend
echo Waiting for frontend to start...
timeout /t 8 >nul

:: Show info
echo.
echo ================================
echo Services Started!
echo ================================
echo.
echo Frontend: http://localhost:%FRONTEND_PORT%
echo Backend:  http://localhost:%BACKEND_PORT%
echo.
echo Opening browser...
start http://localhost:%FRONTEND_PORT%

echo.
echo Press any key to exit...
pause >nul
