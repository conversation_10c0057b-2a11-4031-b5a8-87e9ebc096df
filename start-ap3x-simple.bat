@echo off
setlocal enabledelayedexpansion

:: Simple AP3X Crypto Agent Startup Script
:: Finds available ports and starts both services

echo.
echo ================================
echo   AP3X Crypto Agent Launcher
echo ================================
echo.

:: Check if required directories exist
if not exist "backend" (
    echo [ERROR] Backend directory not found!
    echo Please run this script from the AP3X Crypto Agent root directory.
    pause
    exit /b 1
)

if not exist "UI" (
    echo [ERROR] UI directory not found!
    echo Please run this script from the AP3X Crypto Agent root directory.
    pause
    exit /b 1
)

:: Function to find next available port starting from given port
:find_available_port
set "check_port=%1"
:port_loop
netstat -an | find ":%check_port% " >nul 2>&1
if %errorlevel% equ 0 (
    set /a "check_port+=1"
    goto :port_loop
) else (
    set "available_port=%check_port%"
    exit /b 0
)

:: Find available backend port (starting from 8000)
echo [INFO] Finding available backend port...
call :find_available_port 8000
set "BACKEND_PORT=%available_port%"
echo [SUCCESS] Backend will use port: %BACKEND_PORT%

:: Find available frontend port (starting from 3000)
echo [INFO] Finding available frontend port...
call :find_available_port 3000
set "FRONTEND_PORT=%available_port%"
echo [SUCCESS] Frontend will use port: %FRONTEND_PORT%

:: Update frontend configuration
echo [INFO] Updating frontend to connect to backend on port %BACKEND_PORT%...
cd /d "%~dp0UI"

:: Create backup
if exist "hooks\use-ap3x.ts" (
    copy "hooks\use-ap3x.ts" "hooks\use-ap3x.ts.bak" >nul 2>&1
)

:: Update the hook file with correct backend port
powershell -Command "(Get-Content 'hooks\use-ap3x.ts') -replace 'localhost:8000', 'localhost:%BACKEND_PORT%' | Set-Content 'hooks\use-ap3x.ts'"

echo [SUCCESS] Frontend configured for backend port %BACKEND_PORT%

:: Start backend
echo.
echo [INFO] Starting backend on port %BACKEND_PORT%...
cd /d "%~dp0backend"
start "AP3X Backend" cmd /k "title AP3X Backend (Port %BACKEND_PORT%) && echo Starting backend... && python -m uvicorn api.main:app --host 0.0.0.0 --port %BACKEND_PORT% --reload"

:: Wait for backend to start
echo [INFO] Waiting for backend to initialize...
timeout /t 6 /nobreak >nul

:: Start frontend
echo [INFO] Starting frontend on port %FRONTEND_PORT%...
cd /d "%~dp0UI"
start "AP3X Frontend" cmd /k "title AP3X Frontend (Port %FRONTEND_PORT%) && echo Starting frontend... && npm run dev -- --port %FRONTEND_PORT%"

:: Wait for frontend to start
echo [INFO] Waiting for frontend to initialize...
timeout /t 10 /nobreak >nul

:: Display information
echo.
echo ================================
echo     Services Started!
echo ================================
echo.
echo Frontend: http://localhost:%FRONTEND_PORT%
echo Backend:  http://localhost:%BACKEND_PORT%
echo API Docs: http://localhost:%BACKEND_PORT%/docs
echo.
echo [INFO] Opening frontend in browser...
start "" "http://localhost:%FRONTEND_PORT%"

echo.
echo [INFO] Both services are running in separate windows.
echo [INFO] Close those terminal windows to stop the services.
echo [INFO] Press any key to exit this launcher.
echo.
pause

:: Cleanup - restore original config
echo [INFO] Restoring original frontend configuration...
cd /d "%~dp0UI"
if exist "hooks\use-ap3x.ts.bak" (
    copy "hooks\use-ap3x.ts.bak" "hooks\use-ap3x.ts" >nul 2>&1
    del "hooks\use-ap3x.ts.bak" >nul 2>&1
    echo [SUCCESS] Original configuration restored.
)

exit /b 0
