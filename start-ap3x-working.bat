@echo off
setlocal enabledelayedexpansion

echo.
echo ================================
echo   AP3X Crypto Agent Launcher
echo ================================
echo.

:: Check if we're in the right directory
if not exist "backend\api\main.py" (
    echo [ERROR] Backend not found! Please run from AP3X Crypto Agent root directory.
    echo Current directory: %CD%
    pause
    exit /b 1
)

if not exist "UI\package.json" (
    echo [ERROR] UI not found! Please run from AP3X Crypto Agent root directory.
    echo Current directory: %CD%
    pause
    exit /b 1
)

:: Simple port finding function
set "BACKEND_PORT=8000"
set "FRONTEND_PORT=3000"

:find_backend_port
netstat -an | findstr ":!BACKEND_PORT! " >nul 2>&1
if !errorlevel! equ 0 (
    set /a BACKEND_PORT+=1
    goto find_backend_port
)

:find_frontend_port
netstat -an | findstr ":!FRONTEND_PORT! " >nul 2>&1
if !errorlevel! equ 0 (
    set /a FRONTEND_PORT+=1
    goto find_frontend_port
)

echo [INFO] Backend will use port: !BACKEND_PORT!
echo [INFO] Frontend will use port: !FRONTEND_PORT!

:: Update frontend configuration
echo [INFO] Updating frontend configuration...
cd UI

:: Create a simple backup
if exist "hooks\use-ap3x.ts" (
    copy "hooks\use-ap3x.ts" "hooks\use-ap3x.ts.original" >nul 2>&1
)

:: Use a more reliable method to update the file
powershell -Command "try { $content = Get-Content 'hooks\use-ap3x.ts' -Raw; $content = $content -replace 'localhost:8000', 'localhost:!BACKEND_PORT!'; Set-Content 'hooks\use-ap3x.ts' -Value $content; Write-Host 'Configuration updated successfully' } catch { Write-Host 'Error updating configuration:' $_.Exception.Message }"

if !errorlevel! neq 0 (
    echo [WARNING] Could not update frontend configuration automatically
    echo [INFO] You may need to manually update the backend URL in hooks\use-ap3x.ts
)

:: Start backend
echo.
echo [INFO] Starting backend...
cd ..\backend
start "AP3X Backend" cmd /c "echo Starting AP3X Backend on port !BACKEND_PORT!... && echo. && python -m uvicorn api.main:app --host 0.0.0.0 --port !BACKEND_PORT! --reload && pause"

:: Wait a bit for backend to start
echo [INFO] Waiting for backend to initialize...
timeout /t 8 /nobreak >nul

:: Start frontend
echo [INFO] Starting frontend...
cd ..\UI
start "AP3X Frontend" cmd /c "echo Starting AP3X Frontend on port !FRONTEND_PORT!... && echo. && npm run dev -- --port !FRONTEND_PORT! && pause"

:: Wait for frontend
echo [INFO] Waiting for frontend to initialize...
timeout /t 12 /nobreak >nul

:: Show results
echo.
echo ================================
echo     Services Started!
echo ================================
echo.
echo Frontend: http://localhost:!FRONTEND_PORT!
echo Backend:  http://localhost:!BACKEND_PORT!
echo API Docs: http://localhost:!BACKEND_PORT!/docs
echo.

:: Try to open browser
echo [INFO] Attempting to open browser...
start "" "http://localhost:!FRONTEND_PORT!" 2>nul

echo.
echo [INFO] Both services should be running in separate windows.
echo [INFO] If you don't see them, check for error messages above.
echo [INFO] Press any key to exit this launcher (services will continue).
echo.
pause

:: Restore original configuration
echo [INFO] Restoring original configuration...
cd UI
if exist "hooks\use-ap3x.ts.original" (
    copy "hooks\use-ap3x.ts.original" "hooks\use-ap3x.ts" >nul 2>&1
    del "hooks\use-ap3x.ts.original" >nul 2>&1
    echo [INFO] Configuration restored.
)

exit /b 0
