@echo off
setlocal enabledelayedexpansion

:: AP3X Crypto Agent Startup Script
:: Dynamically finds available ports and starts frontend and backend

echo.
echo ========================================
echo    AP3X Crypto Agent Startup Script
echo ========================================
echo.

:: Set default ports to try
set "BACKEND_PORT_START=8000"
set "FRONTEND_PORT_START=3000"
set "MAX_PORT_ATTEMPTS=10"

:: Function to check if port is available
:check_port
set "port=%1"
netstat -an | find ":%port% " >nul 2>&1
if %errorlevel% equ 0 (
    exit /b 1
) else (
    exit /b 0
)

:: Find available backend port
echo [INFO] Finding available backend port...
set "BACKEND_PORT="
for /l %%i in (%BACKEND_PORT_START%,1,%BACKEND_PORT_START%+%MAX_PORT_ATTEMPTS%) do (
    call :check_port %%i
    if !errorlevel! equ 0 (
        set "BACKEND_PORT=%%i"
        goto :found_backend_port
    )
)

:found_backend_port
if "%BACKEND_PORT%"=="" (
    echo [ERROR] Could not find available backend port in range %BACKEND_PORT_START%-%BACKEND_PORT_START%+%MAX_PORT_ATTEMPTS%
    pause
    exit /b 1
)
echo [SUCCESS] Backend will use port: %BACKEND_PORT%

:: Find available frontend port
echo [INFO] Finding available frontend port...
set "FRONTEND_PORT="
for /l %%i in (%FRONTEND_PORT_START%,1,%FRONTEND_PORT_START%+%MAX_PORT_ATTEMPTS%) do (
    call :check_port %%i
    if !errorlevel! equ 0 (
        set "FRONTEND_PORT=%%i"
        goto :found_frontend_port
    )
)

:found_frontend_port
if "%FRONTEND_PORT%"=="" (
    echo [ERROR] Could not find available frontend port in range %FRONTEND_PORT_START%-%FRONTEND_PORT_START%+%MAX_PORT_ATTEMPTS%
    pause
    exit /b 1
)
echo [SUCCESS] Frontend will use port: %FRONTEND_PORT%

:: Update frontend configuration with backend port
echo [INFO] Updating frontend configuration...
cd /d "%~dp0UI"
if not exist "hooks" mkdir hooks

:: Create or update the config file with dynamic backend URL
echo const BACKEND_URL = "http://localhost:%BACKEND_PORT%"; > hooks\backend-config.js
echo export { BACKEND_URL }; >> hooks\backend-config.js

:: Update the hook file to use dynamic backend URL
powershell -Command "(Get-Content 'hooks\use-ap3x.ts') -replace 'http://localhost:8000', 'http://localhost:%BACKEND_PORT%' | Set-Content 'hooks\use-ap3x.ts'"

echo [SUCCESS] Frontend configured to use backend at http://localhost:%BACKEND_PORT%

:: Start backend in new window
echo [INFO] Starting backend on port %BACKEND_PORT%...
cd /d "%~dp0backend"
start "AP3X Backend (Port %BACKEND_PORT%)" cmd /k "python -m uvicorn api.main:app --host 0.0.0.0 --port %BACKEND_PORT% --reload"

:: Wait a moment for backend to start
echo [INFO] Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

:: Start frontend in new window
echo [INFO] Starting frontend on port %FRONTEND_PORT%...
cd /d "%~dp0UI"
start "AP3X Frontend (Port %FRONTEND_PORT%)" cmd /k "npm run dev -- --port %FRONTEND_PORT%"

:: Wait a moment for frontend to start
echo [INFO] Waiting for frontend to initialize...
timeout /t 8 /nobreak >nul

:: Display startup information
echo.
echo ========================================
echo        AP3X Crypto Agent Started!
echo ========================================
echo.
echo Backend:  http://localhost:%BACKEND_PORT%
echo Frontend: http://localhost:%FRONTEND_PORT%
echo.
echo API Docs: http://localhost:%BACKEND_PORT%/docs
echo Health:   http://localhost:%BACKEND_PORT%/health
echo.
echo [INFO] Opening frontend in default browser...
start "" "http://localhost:%FRONTEND_PORT%"

echo.
echo [INFO] Both services are starting in separate windows.
echo [INFO] Check the terminal windows for startup logs.
echo [INFO] Press any key to exit this script (services will continue running).
echo.
pause

exit /b 0
