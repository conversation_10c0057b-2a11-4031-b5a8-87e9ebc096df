# AP3X Crypto Agent PowerShell Launcher
# Dynamically finds available ports and starts both frontend and backend

param(
    [int]$BackendStartPort = 8000,
    [int]$FrontendStartPort = 3000,
    [switch]$NoRestore
)

# Set execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Cyan"
    White = "White"
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Test-PortAvailable {
    param([int]$Port)
    try {
        $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $Port)
        $listener.Start()
        $listener.Stop()
        return $true
    }
    catch {
        return $false
    }
}

function Find-AvailablePort {
    param([int]$StartPort)
    $port = $StartPort
    while (-not (Test-PortAvailable -Port $port)) {
        $port++
        if ($port -gt ($StartPort + 100)) {
            throw "Could not find available port starting from $StartPort"
        }
    }
    return $port
}

function Update-FrontendConfig {
    param([int]$BackendPort)
    
    $hookFile = "UI\hooks\use-ap3x.ts"
    
    if (-not (Test-Path $hookFile)) {
        Write-ColorOutput "[ERROR] Frontend hook file not found: $hookFile" "Red"
        return $false
    }
    
    # Create backup
    if (-not $NoRestore) {
        Copy-Item $hookFile "$hookFile.backup" -Force
        Write-ColorOutput "[INFO] Created backup of frontend configuration" "Blue"
    }
    
    # Update the file
    try {
        $content = Get-Content $hookFile -Raw
        $content = $content -replace 'localhost:8000', "localhost:$BackendPort"
        $content = $content -replace 'http://localhost:\d+', "http://localhost:$BackendPort"
        Set-Content $hookFile -Value $content
        Write-ColorOutput "[SUCCESS] Frontend configured for backend port $BackendPort" "Green"
        return $true
    }
    catch {
        Write-ColorOutput "[ERROR] Failed to update frontend configuration: $_" "Red"
        return $false
    }
}

function Start-Backend {
    param([int]$Port)
    
    Write-ColorOutput "[INFO] Starting backend on port $Port..." "Blue"
    
    $backendPath = Resolve-Path "backend"
    $startInfo = New-Object System.Diagnostics.ProcessStartInfo
    $startInfo.FileName = "cmd.exe"
    $startInfo.Arguments = "/k `"title AP3X Backend (Port $Port) && cd /d `"$backendPath`" && python -m uvicorn api.main:app --host 0.0.0.0 --port $Port --reload`""
    $startInfo.UseShellExecute = $true
    $startInfo.WindowStyle = "Normal"
    
    try {
        $process = [System.Diagnostics.Process]::Start($startInfo)
        Write-ColorOutput "[SUCCESS] Backend started in new window (PID: $($process.Id))" "Green"
        return $process
    }
    catch {
        Write-ColorOutput "[ERROR] Failed to start backend: $_" "Red"
        return $null
    }
}

function Start-Frontend {
    param([int]$Port)
    
    Write-ColorOutput "[INFO] Starting frontend on port $Port..." "Blue"
    
    $frontendPath = Resolve-Path "UI"
    $startInfo = New-Object System.Diagnostics.ProcessStartInfo
    $startInfo.FileName = "cmd.exe"
    $startInfo.Arguments = "/k `"title AP3X Frontend (Port $Port) && cd /d `"$frontendPath`" && npm run dev -- --port $Port`""
    $startInfo.UseShellExecute = $true
    $startInfo.WindowStyle = "Normal"
    
    try {
        $process = [System.Diagnostics.Process]::Start($startInfo)
        Write-ColorOutput "[SUCCESS] Frontend started in new window (PID: $($process.Id))" "Green"
        return $process
    }
    catch {
        Write-ColorOutput "[ERROR] Failed to start frontend: $_" "Red"
        return $null
    }
}

function Test-BackendHealth {
    param([int]$Port, [int]$TimeoutSeconds = 30)
    
    Write-ColorOutput "[INFO] Waiting for backend to become ready..." "Blue"
    
    $timeout = (Get-Date).AddSeconds($TimeoutSeconds)
    while ((Get-Date) -lt $timeout) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$Port/health" -TimeoutSec 2 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-ColorOutput "[SUCCESS] Backend is healthy!" "Green"
                return $true
            }
        }
        catch {
            Start-Sleep -Seconds 1
        }
    }
    
    Write-ColorOutput "[WARNING] Backend health check timed out" "Yellow"
    return $false
}

function Restore-FrontendConfig {
    $hookFile = "UI\hooks\use-ap3x.ts"
    $backupFile = "$hookFile.backup"
    
    if (Test-Path $backupFile) {
        try {
            Copy-Item $backupFile $hookFile -Force
            Remove-Item $backupFile -Force
            Write-ColorOutput "[SUCCESS] Original frontend configuration restored" "Green"
        }
        catch {
            Write-ColorOutput "[WARNING] Failed to restore original configuration: $_" "Yellow"
        }
    }
}

# Main execution
try {
    Clear-Host
    Write-ColorOutput "========================================" "Blue"
    Write-ColorOutput "    AP3X Crypto Agent PowerShell Launcher" "Blue"
    Write-ColorOutput "========================================" "Blue"
    Write-Host ""
    
    # Check if we're in the right directory
    if (-not (Test-Path "backend") -or -not (Test-Path "UI")) {
        Write-ColorOutput "[ERROR] Please run this script from the AP3X Crypto Agent root directory" "Red"
        Write-ColorOutput "Required directories: backend, UI" "Red"
        exit 1
    }
    
    # Find available ports
    Write-ColorOutput "[INFO] Finding available ports..." "Blue"
    $backendPort = Find-AvailablePort -StartPort $BackendStartPort
    $frontendPort = Find-AvailablePort -StartPort $FrontendStartPort
    
    Write-ColorOutput "[SUCCESS] Backend port: $backendPort" "Green"
    Write-ColorOutput "[SUCCESS] Frontend port: $frontendPort" "Green"
    
    # Update frontend configuration
    if (-not (Update-FrontendConfig -BackendPort $backendPort)) {
        exit 1
    }
    
    # Start backend
    $backendProcess = Start-Backend -Port $backendPort
    if (-not $backendProcess) {
        exit 1
    }
    
    # Wait for backend to be ready
    Test-BackendHealth -Port $backendPort | Out-Null
    
    # Start frontend
    $frontendProcess = Start-Frontend -Port $frontendPort
    if (-not $frontendProcess) {
        exit 1
    }
    
    # Wait for frontend to initialize
    Write-ColorOutput "[INFO] Waiting for frontend to initialize..." "Blue"
    Start-Sleep -Seconds 8
    
    # Display success information
    Write-Host ""
    Write-ColorOutput "========================================" "Green"
    Write-ColorOutput "       AP3X Crypto Agent Ready!" "Green"
    Write-ColorOutput "========================================" "Green"
    Write-Host ""
    Write-ColorOutput "Frontend:  http://localhost:$frontendPort" "White"
    Write-ColorOutput "Backend:   http://localhost:$backendPort" "White"
    Write-ColorOutput "API Docs:  http://localhost:$backendPort/docs" "White"
    Write-ColorOutput "Health:    http://localhost:$backendPort/health" "White"
    Write-Host ""
    
    # Open browser
    Write-ColorOutput "[INFO] Opening AP3X Crypto Agent in browser..." "Blue"
    Start-Process "http://localhost:$frontendPort"
    
    Write-Host ""
    Write-ColorOutput "[INFO] Both services are running in separate windows" "Blue"
    Write-ColorOutput "[INFO] Close those windows to stop the services" "Blue"
    Write-ColorOutput "[INFO] Press any key to exit this launcher..." "Blue"
    
    # Wait for user input
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    
}
catch {
    Write-ColorOutput "[ERROR] An error occurred: $_" "Red"
    exit 1
}
finally {
    # Cleanup
    if (-not $NoRestore) {
        Write-ColorOutput "[INFO] Cleaning up..." "Blue"
        Restore-FrontendConfig
    }
}

Write-ColorOutput "[INFO] Launcher exited. Services continue running in their windows." "Blue"
