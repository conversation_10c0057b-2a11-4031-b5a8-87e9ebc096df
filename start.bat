@echo off
title AP3X Crypto Agent Launcher

echo Starting AP3X Crypto Agent...
echo.

:: Start backend
echo [1/2] Starting backend...
cd backend
start "AP3X Backend" cmd /k python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload

:: Wait a moment
timeout /t 3 >nul

:: Start frontend  
echo [2/2] Starting frontend...
cd ..\UI
start "AP3X Frontend" cmd /k npm run dev

:: Wait for services to start
echo.
echo Waiting for services to start...
timeout /t 10 >nul

:: Open browser
echo Opening browser...
start http://localhost:3000

echo.
echo Done! Check the opened windows for any errors.
echo Frontend: http://localhost:3000
echo Backend: http://localhost:8000
echo.
pause
