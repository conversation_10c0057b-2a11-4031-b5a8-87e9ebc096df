#!/bin/bash

echo "Starting AP3X Crypto Agent Backend..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Error: .env file not found. Please run setup.py first."
    exit 1
fi

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "Warning: Redis is not running. Starting Redis..."
    redis-server --daemonize yes
fi

# Start the agent
echo "Starting FastAPI server..."
python main.py
