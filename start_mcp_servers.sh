#!/bin/bash

echo "Starting MCP servers for AP3X Crypto Agent..."

# Start Sequential Thinking MCP server
echo "Starting Sequential Thinking MCP server on port 3000..."
npx -y @modelcontextprotocol/server-sequential-thinking &
SEQUENTIAL_PID=$!

# Start Moralis MCP server
echo "Starting Moralis MCP server on port 3001..."
MORALIS_API_KEY=${MORALIS_API_KEY} npx @moralisweb3/api-mcp-server --port 3001 &
MORALIS_PID=$!

# Start Fetch MCP server
echo "Starting Fetch MCP server on port 3002..."
uvx mcp-server-fetch --port 3002 &
FETCH_PID=$!

echo "MCP servers started:"
echo "  Sequential Thinking: PID $SEQUENTIAL_PID (port 3000)"
echo "  Moralis: PID $MORALIS_PID (port 3001)"
echo "  Fetch: PID $FETCH_PID (port 3002)"

echo "To stop all MCP servers, run: kill $SEQUENTIAL_PID $MORALIS_PID $FETCH_PID"

# Wait for all background processes
wait
