"""
Test Suite for AP3X Crypto Agent Backend
Comprehensive unit and integration tests
"""

import asyncio
import json
import pytest
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from httpx import AsyncClient

from main import app
from config import TestingSettings
from agent_service import AgentService
from session_manager import <PERSON><PERSON>anager
from tool_registry import ToolRegistry
from error_handling import error_handler, health_checker


# Test Configuration
@pytest.fixture
def test_settings():
    """Test settings fixture"""
    return TestingSettings()


@pytest.fixture
def test_client():
    """Test client fixture"""
    return TestClient(app)


@pytest.fixture
async def async_client():
    """Async test client fixture"""
    async with Async<PERSON>lient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def mock_agent_service():
    """Mock agent service fixture"""
    mock_service = Mock(spec=AgentService)
    mock_service.process_query = AsyncMock(return_value={
        "final_answer": "Test response",
        "thinking_steps": ["Step 1", "Step 2"],
        "confidence_score": 0.8,
        "status": "success"
    })
    mock_service.get_status = AsyncMock(return_value="healthy")
    mock_service.check_external_services = AsyncMock(return_value={
        "moralis": "healthy",
        "web_research": "healthy",
        "twitter": "healthy"
    })
    return mock_service


@pytest.fixture
def mock_redis():
    """Mock Redis fixture"""
    mock_redis = AsyncMock()
    mock_redis.ping = AsyncMock(return_value=True)
    mock_redis.get = AsyncMock(return_value=None)
    mock_redis.setex = AsyncMock(return_value=True)
    mock_redis.delete = AsyncMock(return_value=True)
    return mock_redis


# Unit Tests
class TestHealthEndpoint:
    """Test health check endpoint"""
    
    def test_health_check_success(self, test_client, mock_agent_service):
        """Test successful health check"""
        with patch('main.get_agent_service', return_value=mock_agent_service):
            response = test_client.get("/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] in ["healthy", "degraded"]
            assert "version" in data
            assert "agent_status" in data
            assert "external_services" in data
    
    def test_health_check_unhealthy_agent(self, test_client, mock_agent_service):
        """Test health check with unhealthy agent"""
        mock_agent_service.get_status = AsyncMock(return_value="unhealthy")
        
        with patch('main.get_agent_service', return_value=mock_agent_service):
            response = test_client.get("/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "unhealthy"


class TestQueryEndpoint:
    """Test query processing endpoint"""
    
    def test_query_success(self, test_client, mock_agent_service):
        """Test successful query processing"""
        with patch('main.get_agent_service', return_value=mock_agent_service):
            response = test_client.post("/query", json={
                "query": "What is Bitcoin?",
                "stream": False
            })
            
            assert response.status_code == 200
            data = response.json()
            assert data["final_answer"] == "Test response"
            assert data["status"] == "success"
            assert len(data["thinking_steps"]) == 2
    
    def test_query_streaming_request(self, test_client, mock_agent_service):
        """Test streaming query request"""
        with patch('main.get_agent_service', return_value=mock_agent_service):
            response = test_client.post("/query", json={
                "query": "What is Ethereum?",
                "stream": True
            })
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "streaming"
    
    def test_query_invalid_input(self, test_client, mock_agent_service):
        """Test query with invalid input"""
        with patch('main.get_agent_service', return_value=mock_agent_service):
            response = test_client.post("/query", json={
                "stream": False
                # Missing required 'query' field
            })
            
            assert response.status_code == 422  # Validation error


class TestSessionManagement:
    """Test session management functionality"""
    
    @pytest.mark.asyncio
    async def test_session_creation(self, mock_redis, test_settings):
        """Test session creation"""
        session_manager = SessionManager(mock_redis, test_settings)
        
        session = await session_manager.create_session("test-session-123")
        
        assert session.session_id == "test-session-123"
        assert session.status == "active"
        assert session.message_count == 0
    
    @pytest.mark.asyncio
    async def test_session_activity_update(self, mock_redis, test_settings):
        """Test session activity update"""
        session_manager = SessionManager(mock_redis, test_settings)
        
        # Mock existing session
        mock_redis.get.return_value = json.dumps({
            "session_id": "test-session",
            "created_at": datetime.utcnow().isoformat(),
            "last_activity": datetime.utcnow().isoformat(),
            "message_count": 0,
            "status": "active"
        })
        
        result = await session_manager.update_session_activity("test-session")
        
        assert result is True
        mock_redis.setex.assert_called()


class TestToolRegistry:
    """Test tool registry functionality"""
    
    @pytest.mark.asyncio
    async def test_tool_initialization(self, test_settings):
        """Test tool registry initialization"""
        with patch('tool_registry.MoralisMCPClient') as mock_moralis:
            mock_moralis.return_value.get_wallet_info = AsyncMock(return_value=Mock())
            
            tool_registry = ToolRegistry(test_settings)
            await tool_registry.initialize()
            
            assert "moralis" in tool_registry.tools
            assert tool_registry._initialized is True
    
    @pytest.mark.asyncio
    async def test_health_check_all(self, test_settings):
        """Test health check for all tools"""
        tool_registry = ToolRegistry(test_settings)
        tool_registry._initialized = True
        tool_registry.tools = {"test_tool": Mock()}
        tool_registry.tool_status = {
            "test_tool": Mock(status="healthy", last_check="2024-01-01T00:00:00")
        }
        
        results = await tool_registry.health_check_all()
        
        assert "test_tool" in results


class TestErrorHandling:
    """Test error handling functionality"""
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling and metrics"""
        test_error = ValueError("Test error")
        
        error_info = await error_handler.handle_error(
            test_error,
            context={"test": "context"},
            user_message="Test user message"
        )
        
        assert error_info.error_type == "ValueError"
        assert error_info.message == "Test error"
        assert error_info.context["test"] == "context"
        assert error_info.user_message == "Test user message"
    
    def test_error_metrics_update(self):
        """Test error metrics update"""
        initial_count = error_handler.get_metrics().total_errors
        
        # Simulate error
        asyncio.run(error_handler.handle_error(RuntimeError("Test error")))
        
        metrics = error_handler.get_metrics()
        assert metrics.total_errors == initial_count + 1
        assert "RuntimeError" in metrics.errors_by_type


class TestWebSocketHandlers:
    """Test WebSocket functionality"""
    
    @pytest.mark.asyncio
    async def test_websocket_connection(self, async_client):
        """Test WebSocket connection"""
        # This is a basic test - full WebSocket testing requires more setup
        response = await async_client.get("/ws/status")
        assert response.status_code == 200
        
        data = response.json()
        assert "total_connections" in data
        assert "streaming_sessions" in data


# Integration Tests
class TestIntegration:
    """Integration tests for the complete system"""
    
    @pytest.mark.asyncio
    async def test_full_query_flow(self, async_client, mock_agent_service):
        """Test complete query processing flow"""
        with patch('main.get_agent_service', return_value=mock_agent_service):
            # Test health check first
            health_response = await async_client.get("/health")
            assert health_response.status_code == 200
            
            # Test query processing
            query_response = await async_client.post("/query", json={
                "query": "What is the price of Bitcoin?",
                "stream": False
            })
            assert query_response.status_code == 200
            
            data = query_response.json()
            assert data["final_answer"] == "Test response"
    
    @pytest.mark.asyncio
    async def test_error_monitoring_flow(self, async_client):
        """Test error monitoring endpoints"""
        # Get error metrics
        metrics_response = await async_client.get("/monitoring/errors")
        assert metrics_response.status_code == 200
        
        # Get health status
        health_response = await async_client.get("/monitoring/health")
        assert health_response.status_code == 200
        
        # Reset metrics
        reset_response = await async_client.post("/monitoring/reset-errors")
        assert reset_response.status_code == 200


# Performance Tests
class TestPerformance:
    """Performance and load tests"""
    
    @pytest.mark.asyncio
    async def test_concurrent_queries(self, async_client, mock_agent_service):
        """Test handling concurrent queries"""
        with patch('main.get_agent_service', return_value=mock_agent_service):
            # Create multiple concurrent requests
            tasks = []
            for i in range(10):
                task = async_client.post("/query", json={
                    "query": f"Test query {i}",
                    "stream": False
                })
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            
            # All requests should succeed
            for response in responses:
                assert response.status_code == 200
    
    def test_health_check_performance(self, test_client, mock_agent_service):
        """Test health check response time"""
        import time
        
        with patch('main.get_agent_service', return_value=mock_agent_service):
            start_time = time.time()
            response = test_client.get("/health")
            end_time = time.time()
            
            assert response.status_code == 200
            assert (end_time - start_time) < 1.0  # Should respond within 1 second


# Test Utilities
def create_test_session_data():
    """Create test session data"""
    return {
        "session_id": "test-session-123",
        "created_at": datetime.utcnow().isoformat(),
        "last_activity": datetime.utcnow().isoformat(),
        "message_count": 5,
        "status": "active"
    }


def create_test_query_result():
    """Create test query result"""
    return {
        "final_answer": "Test answer",
        "thinking_steps": ["Step 1", "Step 2", "Step 3"],
        "confidence_score": 0.85,
        "blockchain_data": {"test": "data"},
        "web_research_data": {"test": "research"},
        "status": "success"
    }


# Fixtures for external service mocking
@pytest.fixture
def mock_moralis_client():
    """Mock Moralis client"""
    mock_client = AsyncMock()
    mock_client.get_wallet_info = AsyncMock(return_value=Mock(
        balance="1.5 ETH",
        net_worth="$3000"
    ))
    return mock_client


@pytest.fixture
def mock_twitter_client():
    """Mock Twitter client"""
    mock_client = AsyncMock()
    mock_client.search_crypto_tweets = AsyncMock(return_value=[])
    mock_client.analyze_sentiment = AsyncMock(return_value={
        "sentiment": "neutral",
        "confidence": 0.5
    })
    return mock_client


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
